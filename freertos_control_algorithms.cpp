// freertos_control_algorithms.cpp
// FreeRTOS控制算法层实现 - PID控制和相位控制
#include "freertos_architecture_design.h"
#include "freertos_missing_definitions.h"
#include <PID_v1.h>
#include "digital_pll_integration.h"
#include <PID_v1.h>

// ============================================================================
// 控制算法层全局变量
// ============================================================================

// PID控制器对象
double Input, Output, Setpoint;
double Kp = 2.0, Ki = 0.5, Kd = 1.0;
PID myPID(&Input, &Output, &Setpoint, Kp, Ki, Kd, DIRECT);

// 数字锁相环系统
DigitalPLLIntegration* pll_system = nullptr;

// 控制模式
enum ControlMode {
    CONTROL_MANUAL,
    CONTROL_PID_AUTO,
    CONTROL_CURVE_FOLLOW,
    CONTROL_ADAPTIVE
};

ControlMode current_control_mode = CONTROL_MANUAL;

// 自适应控制参数
struct AdaptiveParams {
    float temp_error_threshold = 5.0f;
    float kp_adjustment_rate = 0.1f;
    float ki_adjustment_rate = 0.05f;
    float kd_adjustment_rate = 0.02f;
    bool auto_tuning_enabled = false;
} adaptive_params;

// ============================================================================
// PID控制任务实现
// ============================================================================

void Task_PID_Controller(void *pvParameters) {
    TickType_t xLastWakeTime = xTaskGetTickCount();
    SensorData_t sensor_data;
    ControlCommand_t control_cmd;
    PIDControlData_t pid_data;
    
    Serial.println("[PID] PID控制任务启动 - 优先级3, 10Hz");
    
    // 初始化PID控制器
    myPID.SetMode(AUTOMATIC);
    myPID.SetOutputLimits(0, 100);
    myPID.SetSampleTime(100);  // 100ms采样时间
    
    // 初始化PID数据
    pid_data.setpoint = 0.0f;
    pid_data.input = 0.0f;
    pid_data.output = 0.0f;
    pid_data.kp = Kp;
    pid_data.ki = Ki;
    pid_data.kd = Kd;
    pid_data.auto_mode = true;
    
    while(1) {
        // 1. 获取滤波后的传感器数据
        bool data_available = false;
        if (xSemaphoreTake(xMutexSharedData, pdMS_TO_TICKS(50)) == pdTRUE) {
            sensor_data = g_system_state.current_sensor_data;
            data_available = sensor_data.valid;
            xSemaphoreGive(xMutexSharedData);
        }
        
        if (data_available) {
            // 2. 处理控制命令
            while (xQueueReceive(xQueueControlCmd, &control_cmd, 0) == pdTRUE) {
                switch (control_cmd.command_type) {
                    case CMD_SET_PID_PARAMS:
                        Kp = control_cmd.params.set_pid.kp;
                        Ki = control_cmd.params.set_pid.ki;
                        Kd = control_cmd.params.set_pid.kd;
                        myPID.SetTunings(Kp, Ki, Kd);
                        Serial.printf("[PID] 参数更新: Kp=%.3f, Ki=%.3f, Kd=%.3f\n", Kp, Ki, Kd);
                        break;
                        
                    case CMD_START_ROAST:
                        current_control_mode = CONTROL_PID_AUTO;
                        myPID.SetMode(AUTOMATIC);
                        xEventGroupSetBits(xEventGroupSystem, EVENT_BIT_ROAST_ACTIVE);
                        Serial.println("[PID] 开始烘焙 - 自动PID模式");
                        break;
                        
                    case CMD_STOP_ROAST:
                        current_control_mode = CONTROL_MANUAL;
                        myPID.SetMode(MANUAL);
                        xEventGroupClearBits(xEventGroupSystem, EVENT_BIT_ROAST_ACTIVE);
                        Serial.println("[PID] 停止烘焙 - 手动模式");
                        break;
                }
            }
            
            // 3. 根据控制模式执行不同的控制算法
            switch (current_control_mode) {
                case CONTROL_PID_AUTO:
                    execute_pid_control(sensor_data, &pid_data);
                    break;
                    
                case CONTROL_CURVE_FOLLOW:
                    execute_curve_following(sensor_data, &pid_data);
                    break;
                    
                case CONTROL_ADAPTIVE:
                    execute_adaptive_control(sensor_data, &pid_data);
                    break;
                    
                case CONTROL_MANUAL:
                default:
                    // 手动模式不执行自动控制
                    break;
            }
            
            // 4. 更新共享PID数据
            if (xSemaphoreTake(xMutexSharedData, pdMS_TO_TICKS(10)) == pdTRUE) {
                g_system_state.current_pid_data = pid_data;
                xSemaphoreGive(xMutexSharedData);
            }
        }
        
        // 5. 性能统计
        static uint32_t pid_cycles = 0;
        pid_cycles++;
        if (pid_cycles % 600 == 0) { // 每60秒报告一次
            Serial.printf("[PID] 控制周期: %lu, 模式: %d, 输出: %.1f%%\n", 
                         pid_cycles, current_control_mode, pid_data.output);
        }
        
        vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(CYCLE_PID_CONTROLLER));
    }
}

void execute_pid_control(const SensorData_t& sensor_data, PIDControlData_t* pid_data) {
    // 标准PID控制
    Input = sensor_data.bean_temperature;
    Setpoint = pid_data->setpoint;
    
    if (myPID.Compute()) {
        pid_data->input = Input;
        pid_data->output = Output;
        
        // 发送功率控制命令
        ControlCommand_t power_cmd;
        power_cmd.command_type = CMD_SET_POWER;
        power_cmd.params.set_power.power_percent = Output;
        power_cmd.timestamp = xTaskGetTickCount();
        
        xQueueSend(xQueueControlCmd, &power_cmd, 0);
        
        // 调试输出（限制频率）
        static uint32_t last_debug = 0;
        if (xTaskGetTickCount() - last_debug > pdMS_TO_TICKS(5000)) {
            Serial.printf("[PID] 设定:%.1f°C 当前:%.1f°C 输出:%.1f%%\n", 
                         Setpoint, Input, Output);
            last_debug = xTaskGetTickCount();
        }
    }
}

void execute_curve_following(const SensorData_t& sensor_data, PIDControlData_t* pid_data) {
    // 烘焙曲线跟随控制
    static uint32_t roast_start_time = 0;
    
    // 获取烘焙开始时间
    EventBits_t events = xEventGroupGetBits(xEventGroupSystem);
    if (events & EVENT_BIT_ROAST_ACTIVE) {
        if (roast_start_time == 0) {
            roast_start_time = xTaskGetTickCount();
        }
        
        // 计算烘焙时间（秒）
        uint32_t roast_time = (xTaskGetTickCount() - roast_start_time) / 1000;
        
        // 简单的线性升温曲线示例
        float target_temp = 25.0f + (roast_time * 0.5f); // 每秒升温0.5°C
        target_temp = constrain(target_temp, 25.0f, 220.0f);
        
        pid_data->setpoint = target_temp;
        Setpoint = target_temp;
        
        // 执行PID控制
        execute_pid_control(sensor_data, pid_data);
        
        Serial.printf("[CURVE] 时间:%lus 目标:%.1f°C 当前:%.1f°C\n", 
                     roast_time, target_temp, sensor_data.bean_temperature);
    } else {
        roast_start_time = 0;
    }
}

void execute_adaptive_control(const SensorData_t& sensor_data, PIDControlData_t* pid_data) {
    // 自适应PID控制
    static float last_error = 0.0f;
    static uint32_t stable_count = 0;
    
    float current_error = abs(pid_data->setpoint - sensor_data.bean_temperature);
    
    // 检查是否需要调整PID参数
    if (adaptive_params.auto_tuning_enabled) {
        if (current_error > adaptive_params.temp_error_threshold) {
            // 误差较大，增加比例增益
            Kp += adaptive_params.kp_adjustment_rate;
            stable_count = 0;
        } else if (current_error < 1.0f && abs(current_error - last_error) < 0.5f) {
            // 系统稳定，可以优化积分和微分参数
            stable_count++;
            if (stable_count > 50) { // 5秒稳定
                Ki += adaptive_params.ki_adjustment_rate;
                Kd += adaptive_params.kd_adjustment_rate;
                stable_count = 0;
            }
        }
        
        // 限制参数范围
        Kp = constrain(Kp, 0.1f, 10.0f);
        Ki = constrain(Ki, 0.01f, 2.0f);
        Kd = constrain(Kd, 0.001f, 1.0f);
        
        myPID.SetTunings(Kp, Ki, Kd);
        
        // 更新PID数据
        pid_data->kp = Kp;
        pid_data->ki = Ki;
        pid_data->kd = Kd;
    }
    
    last_error = current_error;
    
    // 执行标准PID控制
    execute_pid_control(sensor_data, pid_data);
}

// ============================================================================
// 相位控制任务实现
// ============================================================================

void Task_Phase_Control(void *pvParameters) {
    TickType_t xLastWakeTime = xTaskGetTickCount();
    
    Serial.println("[PHASE] 相位控制任务启动 - 优先级3, 20Hz");
    
    // 初始化数字锁相环系统
    pll_system = new DigitalPLLIntegration();
    if (pll_system && pll_system->begin(5)) {
        Serial.println("[PHASE] 数字锁相环初始化成功");
    } else {
        Serial.println("[PHASE] 数字锁相环初始化失败");
        vTaskDelete(NULL); // 删除任务
        return;
    }
    
    // 相位控制状态变量
    uint32_t phase_loss_count = 0;
    uint32_t recovery_attempts = 0;
    bool last_pll_locked = false;
    
    while(1) {
        // 1. 更新数字锁相环
        pll_system->update();
        
        // 2. 检查PLL锁定状态
        bool pll_locked = pll_system->isPLLLocked();
        bool phase_connected = pll_system->isPhaseConnected();
        
        if (!pll_locked && last_pll_locked) {
            phase_loss_count++;
            Serial.printf("[PHASE] ⚠️ PLL失锁 (第%lu次)\n", phase_loss_count);
        }
        
        // 3. 相位失联检测和处理
        if (!phase_connected) {
            Serial.println("[PHASE] 🚨 相位失联检测");
            
            // 尝试自动恢复
            if (recovery_attempts < 3) {
                pll_system->forceRecovery();
                recovery_attempts++;
                Serial.printf("[PHASE] 执行恢复程序 (第%lu次)\n", recovery_attempts);
            } else {
                // 多次恢复失败，降级到安全模式
                Serial.println("[PHASE] 🚨 多次恢复失败，启动安全保护");
                System_Emergency_Stop();
            }
        } else {
            recovery_attempts = 0; // 重置恢复计数
        }
        
        // 4. 功率漂移检测
        float power_variance = pll_system->getPowerVariance();
        if (power_variance > 3.0f) { // 3%方差阈值
            Serial.printf("[PHASE] ⚠️ 功率漂移检测: %.2f%%\n", power_variance);
            
            // 触发功率校正
            // 这里可以发送校正命令到PWM控制任务
        }
        
        // 5. 系统稳定性评估
        float stability_score = pll_system->getStabilityScore();
        if (stability_score < 80.0f) {
            Serial.printf("[PHASE] ⚠️ 系统稳定性低: %.1f/100\n", stability_score);
        }
        
        // 6. 更新系统状态
        static uint32_t status_update_count = 0;
        status_update_count++;
        if (status_update_count % 1000 == 0) { // 每50秒更新一次
            Serial.printf("[PHASE] PLL锁定:%s 相位连接:%s 稳定性:%.1f 方差:%.2f%%\n",
                         pll_locked ? "是" : "否",
                         phase_connected ? "是" : "否", 
                         stability_score, power_variance);
        }
        
        last_pll_locked = pll_locked;
        
        vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(CYCLE_PHASE_CONTROL));
    }
}

// ============================================================================
// 控制算法层工具函数
// ============================================================================

bool Control_Set_PID_Parameters(float kp, float ki, float kd) {
    ControlCommand_t cmd;
    cmd.command_type = CMD_SET_PID_PARAMS;
    cmd.params.set_pid.kp = kp;
    cmd.params.set_pid.ki = ki;
    cmd.params.set_pid.kd = kd;
    cmd.timestamp = xTaskGetTickCount();
    
    return xQueueSend(xQueueControlCmd, &cmd, pdMS_TO_TICKS(100)) == pdTRUE;
}

bool Control_Start_Roast(float target_temp) {
    ControlCommand_t cmd;
    cmd.command_type = CMD_START_ROAST;
    cmd.timestamp = xTaskGetTickCount();
    
    // 设置目标温度
    Setpoint = target_temp;
    
    return xQueueSend(xQueueControlCmd, &cmd, pdMS_TO_TICKS(100)) == pdTRUE;
}

bool Control_Stop_Roast(void) {
    ControlCommand_t cmd;
    cmd.command_type = CMD_STOP_ROAST;
    cmd.timestamp = xTaskGetTickCount();
    
    return xQueueSend(xQueueControlCmd, &cmd, pdMS_TO_TICKS(100)) == pdTRUE;
}

void Control_Set_Mode(ControlMode mode) {
    current_control_mode = mode;
    Serial.printf("[CONTROL] 控制模式切换: %d\n", mode);
}

void Control_Enable_Adaptive(bool enable) {
    adaptive_params.auto_tuning_enabled = enable;
    Serial.printf("[CONTROL] 自适应控制: %s\n", enable ? "启用" : "禁用");
}

void Control_Print_Status(void) {
    Serial.println("========== 控制算法层状态 ==========");
    Serial.printf("控制模式: %d\n", current_control_mode);
    Serial.printf("PID参数: Kp=%.3f, Ki=%.3f, Kd=%.3f\n", Kp, Ki, Kd);
    Serial.printf("设定温度: %.1f°C\n", Setpoint);
    Serial.printf("当前输入: %.1f°C\n", Input);
    Serial.printf("PID输出: %.1f%%\n", Output);
    
    if (pll_system) {
        Serial.printf("PLL锁定: %s\n", pll_system->isPLLLocked() ? "是" : "否");
        Serial.printf("相位连接: %s\n", pll_system->isPhaseConnected() ? "是" : "否");
        Serial.printf("稳定性: %.1f/100\n", pll_system->getStabilityScore());
        Serial.printf("功率方差: %.2f%%\n", pll_system->getPowerVariance());
    }
    
    Serial.printf("自适应控制: %s\n", adaptive_params.auto_tuning_enabled ? "启用" : "禁用");
    Serial.println("==================================");
}

// ============================================================================
// 控制算法测试函数
// ============================================================================

void Control_Test_PID_Response(void) {
    Serial.println("========== PID响应测试 ==========");
    
    // 测试不同的PID参数
    float test_params[][3] = {
        {1.0f, 0.3f, 0.5f},   // 保守参数
        {2.0f, 0.5f, 1.0f},   // 标准参数
        {3.0f, 0.8f, 1.5f}    // 激进参数
    };
    
    for (int i = 0; i < 3; i++) {
        Serial.printf("测试参数组 %d: Kp=%.1f, Ki=%.1f, Kd=%.1f\n", 
                     i+1, test_params[i][0], test_params[i][1], test_params[i][2]);
        
        Control_Set_PID_Parameters(test_params[i][0], test_params[i][1], test_params[i][2]);
        vTaskDelay(pdMS_TO_TICKS(5000)); // 等待5秒观察响应
        
        Control_Print_Status();
    }
    
    Serial.println("✅ PID响应测试完成");
    Serial.println("===============================");
}


