// power_feedback_monitor.cpp
// 功率反馈监测系统实现
#include "power_feedback_monitor.h"

// ============================================================================
// PowerFeedbackMonitor 类实现
// ============================================================================

PowerFeedbackMonitor::PowerFeedbackMonitor() {
    // 初始化功率历史记录
    for (int i = 0; i < POWER_HISTORY_SIZE; i++) {
        power_history[i] = 0.0f;
    }
    
    last_stable_time = millis();
    last_update_time = millis();
}

void PowerFeedbackMonitor::begin() {
    reset();
    Serial.println("[POWER_MONITOR] 功率反馈监测器初始化完成");
}

void PowerFeedbackMonitor::update(float target, float actual) {
    unsigned long current_time = millis();
    
    // 检查更新间隔
    if (current_time - last_update_time < (1000 / FEEDBACK_SAMPLE_RATE)) {
        return;
    }
    
    last_update_time = current_time;
    
    // 更新功率数据
    target_power = target;
    actual_power = actual;
    
    // 记录功率历史
    power_history[history_index] = actual;
    history_index = (history_index + 1) % POWER_HISTORY_SIZE;
    
    // 更新统计分析
    updateStatistics();
    
    // 检测异常
    detectDrift();
    detectPhaseLoss();
    
    // 更新自适应阈值
    updateAdaptiveThreshold();
    
    // 更新状态
    updateStatus();
}

void PowerFeedbackMonitor::updateStatistics() {
    power_mean = calculateMean();
    power_variance = calculateVariance();
    power_trend = calculateTrend();
    drift_rate = calculateDriftRate();
}

float PowerFeedbackMonitor::calculateMean() {
    float sum = 0.0f;
    for (int i = 0; i < POWER_HISTORY_SIZE; i++) {
        sum += power_history[i];
    }
    return sum / POWER_HISTORY_SIZE;
}

float PowerFeedbackMonitor::calculateVariance() {
    float mean = power_mean;
    float sum_sq_diff = 0.0f;
    
    for (int i = 0; i < POWER_HISTORY_SIZE; i++) {
        float diff = power_history[i] - mean;
        sum_sq_diff += diff * diff;
    }
    
    return sqrt(sum_sq_diff / POWER_HISTORY_SIZE);
}

float PowerFeedbackMonitor::calculateTrend() {
    // 使用最小二乘法计算趋势
    float sum_x = 0.0f, sum_y = 0.0f, sum_xy = 0.0f, sum_x2 = 0.0f;
    int n = POWER_HISTORY_SIZE;
    
    for (int i = 0; i < n; i++) {
        float x = (float)i;
        float y = power_history[i];
        sum_x += x;
        sum_y += y;
        sum_xy += x * y;
        sum_x2 += x * x;
    }
    
    float slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x);
    return slope;
}

float PowerFeedbackMonitor::calculateDriftRate() {
    // 计算最近几个点的变化率
    if (history_index < 5) return 0.0f;
    
    int start_idx = (history_index - 5 + POWER_HISTORY_SIZE) % POWER_HISTORY_SIZE;
    int end_idx = (history_index - 1 + POWER_HISTORY_SIZE) % POWER_HISTORY_SIZE;
    
    float start_value = power_history[start_idx];
    float end_value = power_history[end_idx];
    
    return (end_value - start_value) / 5.0f; // 每采样点的变化率
}

void PowerFeedbackMonitor::detectDrift() {
    float power_error = abs(actual_power - target_power);
    
    if (power_error > adaptive_threshold) {
        consecutive_drift_count++;
        
        if (power_error > max_drift_detected) {
            max_drift_detected = power_error;
        }
        
        if (consecutive_drift_count > DRIFT_DETECTION_WINDOW / 4) {
            if (power_error > DRIFT_ALERT_THRESHOLD) {
                handleDriftAlert();
            }
        }
    } else {
        consecutive_drift_count = 0;
        drift_alert_active = false;
        last_stable_time = millis();
    }
}

void PowerFeedbackMonitor::detectPhaseLoss() {
    unsigned long current_time = millis();
    
    // 检查是否长时间不稳定
    if (current_time - last_stable_time > PHASE_LOSS_TIMEOUT) {
        phase_loss_count++;
        handlePhaseAlert();
    }
    
    // 检查功率是否完全失控
    if (power_variance > DRIFT_ALERT_THRESHOLD * 2) {
        phase_loss_count++;
        handlePhaseAlert();
    }
}

void PowerFeedbackMonitor::updateAdaptiveThreshold() {
    // 基于历史方差调整阈值
    float variance_factor = power_variance / STABILITY_THRESHOLD;
    
    if (variance_factor > 1.5f) {
        adaptive_threshold = min(adaptive_threshold * 1.1f, DRIFT_ALERT_THRESHOLD);
    } else if (variance_factor < 0.5f) {
        adaptive_threshold = max(adaptive_threshold * 0.9f, STABILITY_THRESHOLD);
    }
}

void PowerFeedbackMonitor::updateStatus() {
    float power_error = abs(actual_power - target_power);
    
    if (phase_loss_count > 3) {
        current_status = FEEDBACK_PHASE_LOSS;
    } else if (power_error > DRIFT_ALERT_THRESHOLD) {
        current_status = FEEDBACK_MAJOR_DRIFT;
    } else if (power_error > adaptive_threshold) {
        current_status = FEEDBACK_MINOR_DRIFT;
    } else {
        current_status = FEEDBACK_STABLE;
    }
}

void PowerFeedbackMonitor::handleDriftAlert() {
    if (!drift_alert_active) {
        drift_alert_active = true;
        Serial.printf("[POWER_MONITOR] 功率漂移报警！目标:%.1f%% 实际:%.1f%% 误差:%.1f%%\n",
                     target_power, actual_power, abs(actual_power - target_power));
    }
}

void PowerFeedbackMonitor::handlePhaseAlert() {
    Serial.printf("[POWER_MONITOR] 相位失联警告！失联次数:%d 功率方差:%.2f%%\n",
                 phase_loss_count, power_variance);
}

FeedbackStatus PowerFeedbackMonitor::getStatus() {
    return current_status;
}

bool PowerFeedbackMonitor::isStable() {
    return current_status == FEEDBACK_STABLE;
}

bool PowerFeedbackMonitor::isDrifting() {
    return current_status == FEEDBACK_MINOR_DRIFT || current_status == FEEDBACK_MAJOR_DRIFT;
}

bool PowerFeedbackMonitor::isPhaseLossDetected() {
    return current_status == FEEDBACK_PHASE_LOSS;
}

float PowerFeedbackMonitor::getPowerVariance() {
    return power_variance;
}

float PowerFeedbackMonitor::getDriftRate() {
    return drift_rate;
}

float PowerFeedbackMonitor::getPowerTrend() {
    return power_trend;
}

float PowerFeedbackMonitor::getTargetPower() {
    return target_power;
}

float PowerFeedbackMonitor::getActualPower() {
    return actual_power;
}

float PowerFeedbackMonitor::getStabilityScore() {
    // 计算稳定性评分 (0-100)
    float variance_score = max(0.0f, 100.0f - power_variance * 10.0f);
    float drift_score = max(0.0f, 100.0f - abs(drift_rate) * 20.0f);
    float error_score = max(0.0f, 100.0f - abs(actual_power - target_power) * 5.0f);
    
    return (variance_score + drift_score + error_score) / 3.0f;
}

void PowerFeedbackMonitor::reset() {
    // 重置所有状态
    for (int i = 0; i < POWER_HISTORY_SIZE; i++) {
        power_history[i] = 0.0f;
    }
    
    history_index = 0;
    target_power = 0.0f;
    actual_power = 0.0f;
    power_mean = 0.0f;
    power_variance = 0.0f;
    power_trend = 0.0f;
    drift_rate = 0.0f;
    
    current_status = FEEDBACK_STABLE;
    last_stable_time = millis();
    drift_alert_active = false;
    consecutive_drift_count = 0;
    phase_loss_count = 0;
    max_drift_detected = 0.0f;
    
    adaptive_threshold = STABILITY_THRESHOLD;
    
    Serial.println("[POWER_MONITOR] 监测器已重置");
}

void PowerFeedbackMonitor::setThreshold(float threshold) {
    adaptive_threshold = constrain(threshold, 0.5f, 20.0f);
    Serial.printf("[POWER_MONITOR] 阈值设置为: %.2f%%\n", adaptive_threshold);
}

void PowerFeedbackMonitor::printStatus() {
    const char* status_names[] = {
        "稳定", "轻微漂移", "严重漂移", "相位失联", "系统错误"
    };
    
    Serial.println("========== 功率监测状态 ==========");
    Serial.printf("状态: %s\n", status_names[current_status]);
    Serial.printf("目标功率: %.1f%%\n", target_power);
    Serial.printf("实际功率: %.1f%%\n", actual_power);
    Serial.printf("功率误差: %.1f%%\n", abs(actual_power - target_power));
    Serial.printf("功率方差: %.2f%%\n", power_variance);
    Serial.printf("漂移速率: %.3f%%/采样\n", drift_rate);
    Serial.printf("稳定性评分: %.1f/100\n", getStabilityScore());
    Serial.printf("自适应阈值: %.2f%%\n", adaptive_threshold);
    Serial.printf("失联次数: %d\n", phase_loss_count);
    Serial.println("================================");
}

void PowerFeedbackMonitor::printDetailedReport() {
    Serial.println("========== 详细监测报告 ==========");
    printStatus();
    
    Serial.println("\n功率历史记录 (最近10个采样):");
    for (int i = 0; i < min(10, POWER_HISTORY_SIZE); i++) {
        int idx = (history_index - 1 - i + POWER_HISTORY_SIZE) % POWER_HISTORY_SIZE;
        Serial.printf("  [%d] %.2f%%\n", i, power_history[idx]);
    }
    
    Serial.printf("\n统计分析:\n");
    Serial.printf("  平均功率: %.2f%%\n", power_mean);
    Serial.printf("  功率趋势: %.4f\n", power_trend);
    Serial.printf("  最大漂移: %.2f%%\n", max_drift_detected);
    Serial.printf("  连续漂移: %d次\n", consecutive_drift_count);
    
    unsigned long stable_duration = millis() - last_stable_time;
    Serial.printf("  稳定持续: %lu ms\n", stable_duration);
    
    Serial.println("================================");
}

// ============================================================================
// PowerCorrectionController 类实现
// ============================================================================

PowerCorrectionController::PowerCorrectionController(PowerFeedbackMonitor* feedback_monitor) {
    monitor = feedback_monitor;

    // 初始化校正历史
    for (int i = 0; i < 10; i++) {
        correction_history[i] = 0.0f;
    }
}

void PowerCorrectionController::begin() {
    resetCorrection();
    Serial.println("[POWER_CORRECTION] 功率校正控制器初始化完成");
}

void PowerCorrectionController::update() {
    if (!monitor || !auto_correction_enabled) return;

    unsigned long current_time = millis();

    // 限制校正频率
    if (current_time - last_correction_time < 100) { // 最多每100ms校正一次
        return;
    }

    // 检查是否需要校正
    if (monitor->isDrifting()) {
        float target = monitor->getTargetPower();
        float actual = monitor->getActualPower();
        float correction = calculateCorrection(target, actual);

        if (isCorrectionSafe(correction)) {
            // 记录校正
            correction_history[correction_index] = correction;
            correction_index = (correction_index + 1) % 10;
            total_correction += correction;
            correction_count++;
            last_correction_time = current_time;

            Serial.printf("[POWER_CORRECTION] 应用校正: %.2f%% (累计: %.2f%%)\n",
                         correction, total_correction);
        } else {
            Serial.println("[POWER_CORRECTION] 校正被安全系统阻止");
            enterSafetyMode();
        }
    }

    // 更新安全模式
    updateSafetyMode();
}

float PowerCorrectionController::calculateCorrection(float target_power, float actual_power) {
    float error = target_power - actual_power;

    // 使用PID校正算法
    float correction = pidCorrection(error);

    // 限制校正幅度
    correction = constrain(correction, -max_correction, max_correction);

    return correction;
}

float PowerCorrectionController::pidCorrection(float error) {
    static float integral = 0.0f;
    static float last_error = 0.0f;

    // PID参数
    float kp = correction_gain;
    float ki = correction_gain * 0.1f;
    float kd = correction_gain * 0.01f;

    // 比例项
    float proportional = kp * error;

    // 积分项
    integral += error;
    integral = constrain(integral, -100.0f, 100.0f); // 防止积分饱和
    float integral_term = ki * integral;

    // 微分项
    float derivative = error - last_error;
    float derivative_term = kd * derivative;

    last_error = error;

    return proportional + integral_term + derivative_term;
}

float PowerCorrectionController::applyCorrectedPower(float original_power) {
    if (!auto_correction_enabled || safety_mode) {
        return original_power;
    }

    // 计算当前校正量
    float current_correction = 0.0f;
    for (int i = 0; i < 10; i++) {
        current_correction += correction_history[i];
    }
    current_correction /= 10.0f; // 平均校正量

    float corrected_power = original_power + current_correction;
    return constrain(corrected_power, 0.0f, 100.0f);
}

bool PowerCorrectionController::isCorrectionSafe(float correction) {
    // 检查校正幅度
    if (abs(correction) > max_correction) {
        return false;
    }

    // 检查累计校正量
    if (abs(total_correction) > 50.0f) { // 累计校正不超过50%
        return false;
    }

    // 检查校正频率
    if (correction_count > 100) { // 校正次数限制
        return false;
    }

    return true;
}

void PowerCorrectionController::updateSafetyMode() {
    if (safety_mode) {
        // 检查是否可以退出安全模式
        if (monitor && monitor->isStable()) {
            unsigned long stable_time = millis() - last_correction_time;
            if (stable_time > 5000) { // 稳定5秒后退出安全模式
                exitSafetyMode();
            }
        }
    }
}

void PowerCorrectionController::enableAutoCorrection(bool enable) {
    auto_correction_enabled = enable;
    Serial.printf("[POWER_CORRECTION] 自动校正: %s\n", enable ? "开启" : "关闭");
}

void PowerCorrectionController::setCorrectionGain(float gain) {
    correction_gain = constrain(gain, 0.01f, 1.0f);
    Serial.printf("[POWER_CORRECTION] 校正增益设置为: %.3f\n", correction_gain);
}

void PowerCorrectionController::resetCorrection() {
    for (int i = 0; i < 10; i++) {
        correction_history[i] = 0.0f;
    }

    correction_index = 0;
    total_correction = 0.0f;
    correction_count = 0;
    safety_mode = false;

    Serial.println("[POWER_CORRECTION] 校正器已重置");
}

void PowerCorrectionController::enterSafetyMode() {
    safety_mode = true;
    auto_correction_enabled = false;
    Serial.println("[POWER_CORRECTION] 进入安全模式，自动校正已禁用");
}

void PowerCorrectionController::exitSafetyMode() {
    safety_mode = false;
    auto_correction_enabled = true;
    Serial.println("[POWER_CORRECTION] 退出安全模式，自动校正已恢复");
}

bool PowerCorrectionController::isAutoCorrectionEnabled() {
    return auto_correction_enabled;
}

bool PowerCorrectionController::isInSafetyMode() {
    return safety_mode;
}

float PowerCorrectionController::getTotalCorrection() {
    return total_correction;
}

void PowerCorrectionController::printCorrectionStatus() {
    Serial.println("========== 功率校正状态 ==========");
    Serial.printf("自动校正: %s\n", auto_correction_enabled ? "开启" : "关闭");
    Serial.printf("安全模式: %s\n", safety_mode ? "激活" : "正常");
    Serial.printf("校正增益: %.3f\n", correction_gain);
    Serial.printf("最大校正: %.1f%%\n", max_correction);
    Serial.printf("累计校正: %.2f%%\n", total_correction);
    Serial.printf("校正次数: %d\n", correction_count);

    // 显示最近的校正历史
    Serial.println("最近校正历史:");
    for (int i = 0; i < 5; i++) {
        int idx = (correction_index - 1 - i + 10) % 10;
        Serial.printf("  [%d] %.2f%%\n", i, correction_history[idx]);
    }

    if (monitor) {
        Serial.printf("监测器状态: %s\n", monitor->isStable() ? "稳定" : "异常");
    }

    Serial.println("================================");
}
