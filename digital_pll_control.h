// digital_pll_control.h
// 数字锁相环相位控制系统 - 解决相位失联和功率漂移问题
#ifndef DIGITAL_PLL_CONTROL_H
#define DIGITAL_PLL_CONTROL_H

#include <Arduino.h>
#include <driver/ledc.h>
#include <math.h>

// 数字锁相环参数
#define AC_NOMINAL_FREQUENCY 50.0f        // 标称电网频率50Hz
#define AC_FREQUENCY_TOLERANCE 0.5f       // 频率容差±0.5Hz
#define PLL_SAMPLE_RATE 1000              // PLL采样率1kHz
#define PLL_UPDATE_INTERVAL_US 1000       // PLL更新间隔1ms

// 锁相环控制参数
#define PLL_KP 0.1f                       // 比例增益
#define PLL_KI 0.01f                      // 积分增益
#define PLL_KD 0.001f                     // 微分增益
#define PLL_LOCK_THRESHOLD 0.02f          // 锁定阈值(弧度)
#define PLL_UNLOCK_THRESHOLD 0.1f         // 失锁阈值(弧度)

// 功率监测参数
#define POWER_STABILITY_WINDOW 20         // 功率稳定性监测窗口
#define POWER_DRIFT_THRESHOLD 5.0f        // 功率漂移阈值5%
#define PHASE_LOSS_DETECTION_TIME 100     // 相位失联检测时间100ms

/**
 * 数字锁相环相位控制器
 * 实现纯软件的锁相环算法，解决无过零检测系统的相位失联问题
 */
class DigitalPLLControl {
private:
    // 锁相环核心状态
    float vco_frequency = AC_NOMINAL_FREQUENCY;    // 数控振荡器频率
    float vco_phase = 0.0f;                       // 数控振荡器相位
    float reference_phase = 0.0f;                 // 参考相位
    float phase_error = 0.0f;                     // 相位误差
    bool pll_locked = false;                      // 锁相环锁定状态
    
    // PID控制器状态
    float pid_integral = 0.0f;                    // 积分项
    float pid_derivative = 0.0f;                  // 微分项
    float last_phase_error = 0.0f;               // 上次相位误差
    
    // 时间管理
    unsigned long last_update_time = 0;           // 上次更新时间
    unsigned long pll_start_time = 0;             // PLL启动时间
    unsigned long last_zero_cross_time = 0;       // 上次过零时间
    
    // 功率控制参数
    int target_power_percent = 0;                 // 目标功率百分比
    float current_trigger_angle = 0.0f;           // 当前触发角度
    int pwm_channel = 5;                          // PWM通道
    int pwm_frequency = 100;                      // PWM频率
    int pwm_resolution = 12;                      // PWM分辨率
    
    // 功率稳定性监测
    float power_history[POWER_STABILITY_WINDOW];  // 功率历史记录
    int power_history_index = 0;                  // 历史记录索引
    float power_variance = 0.0f;                  // 功率方差
    bool power_stable = true;                     // 功率稳定标志
    
    // 相位失联检测
    unsigned long last_stable_time = 0;           // 上次稳定时间
    int phase_loss_count = 0;                     // 相位失联计数
    bool phase_loss_detected = false;             // 相位失联标志
    
    // 线性化查找表
    float linearization_table[101];               // 功率线性化表
    
public:
    DigitalPLLControl(int channel = 5);
    
    // 初始化数字锁相环
    void begin();
    
    // 主更新函数 - 必须在loop()中高频调用
    void update();
    
    // 设置目标功率
    void setPower(int power_percent);
    
    // 获取当前功率
    int getCurrentPower();
    
    // 获取锁相环状态
    bool isPLLLocked();
    bool isPowerStable();
    bool isPhaseConnected();
    
    // 获取诊断信息
    float getVCOFrequency();
    float getPhaseError();
    float getPowerVariance();
    
    // 重置锁相环
    void reset();
    
    // 调试信息
    void printDebugInfo();
    
private:
    // 锁相环核心算法
    void updatePLL();
    void updateVCO();
    float calculatePhaseError();
    float pidController(float error);
    
    // 功率控制算法
    void updatePowerControl();
    float calculateTriggerAngle(int power_percent);
    int calculatePWMValue();
    
    // 监测和诊断
    void updatePowerMonitoring();
    void updatePhaseDetection();
    bool detectPhaseLoss();
    
    // 辅助函数
    void initializeLinearizationTable();
    float normalizePhase(float phase);
    float calculatePowerVariance();
};

/**
 * 数字锁相环诊断器
 * 提供详细的系统状态监测和故障诊断
 */
class PLLDiagnostics {
private:
    DigitalPLLControl* pll_controller;
    
    // 诊断统计
    unsigned long total_updates = 0;
    unsigned long lock_loss_count = 0;
    unsigned long phase_loss_count = 0;
    unsigned long power_drift_count = 0;
    
    // 性能统计
    float avg_lock_time = 0.0f;
    float max_phase_error = 0.0f;
    float avg_power_stability = 0.0f;
    
public:
    PLLDiagnostics(DigitalPLLControl* controller);
    
    void update();
    void printReport();
    void resetStatistics();
    
    // 获取诊断数据
    float getLockSuccessRate();
    float getAverageLockTime();
    float getPowerStabilityScore();
};

#endif // DIGITAL_PLL_CONTROL_H
