// pll_protection_system.h
// 数字锁相环多重保护系统
#ifndef PLL_PROTECTION_SYSTEM_H
#define PLL_PROTECTION_SYSTEM_H

#include <Arduino.h>
#include "digital_pll_control.h"
#include "power_feedback_monitor.h"

// 保护级别定义
enum ProtectionLevel {
    PROTECTION_NONE,                      // 无保护
    PROTECTION_BASIC,                     // 基础保护
    PROTECTION_ENHANCED,                  // 增强保护
    PROTECTION_MAXIMUM                    // 最大保护
};

// 保护状态
enum ProtectionStatus {
    PROTECTION_NORMAL,                    // 正常状态
    PROTECTION_WARNING,                   // 警告状态
    PROTECTION_ALERT,                     // 报警状态
    PROTECTION_EMERGENCY,                 // 紧急状态
    PROTECTION_SHUTDOWN                   // 关闭状态
};

// 故障类型
enum FaultType {
    FAULT_NONE,                          // 无故障
    FAULT_PHASE_LOSS,                    // 相位失联
    FAULT_POWER_DRIFT,                   // 功率漂移
    FAULT_FREQUENCY_DEVIATION,           // 频率偏差
    FAULT_SYSTEM_OVERLOAD,               // 系统过载
    FAULT_HARDWARE_ERROR,                // 硬件错误
    FAULT_COMMUNICATION_LOSS             // 通信丢失
};

/**
 * 相位失联检测器
 * 多层次检测相位失联，提供早期预警
 */
class PhaseLossDetector {
private:
    // 检测参数
    float phase_error_threshold = 0.1f;          // 相位误差阈值
    int consecutive_error_limit = 5;             // 连续错误限制
    unsigned long detection_window = 1000;       // 检测窗口1秒
    
    // 检测状态
    int consecutive_errors = 0;                  // 连续错误计数
    unsigned long last_good_phase = 0;           // 上次良好相位时间
    bool phase_loss_detected = false;           // 相位失联标志
    
    // 统计数据
    int total_detections = 0;                    // 总检测次数
    int false_alarms = 0;                        // 误报次数
    float detection_accuracy = 0.0f;             // 检测精度
    
public:
    PhaseLossDetector();
    
    void update(float phase_error, bool pll_locked);
    bool isPhaseLossDetected();
    void reset();
    void setThreshold(float threshold);
    float getDetectionAccuracy();
    void printStatus();
};

/**
 * 功率漂移监测器
 * 检测功率异常漂移，防止功率失控
 */
class PowerDriftMonitor {
private:
    // 监测参数
    float drift_threshold = 5.0f;               // 漂移阈值5%
    float rapid_drift_threshold = 10.0f;        // 快速漂移阈值10%
    int monitoring_window = 20;                 // 监测窗口
    
    // 功率历史
    float power_history[50];                    // 功率历史记录
    int history_index = 0;                      // 历史索引
    
    // 漂移检测
    float current_drift_rate = 0.0f;            // 当前漂移速率
    bool drift_detected = false;                // 漂移检测标志
    bool rapid_drift_detected = false;          // 快速漂移标志
    
    // 统计分析
    float max_drift_detected = 0.0f;            // 最大检测漂移
    int drift_events = 0;                       // 漂移事件数
    
public:
    PowerDriftMonitor();
    
    void update(float target_power, float actual_power);
    bool isDriftDetected();
    bool isRapidDriftDetected();
    float getDriftRate();
    void reset();
    void setThresholds(float normal, float rapid);
    void printStatus();
    
private:
    float calculateDriftRate();
    void updateStatistics();
};

/**
 * 频率偏差监测器
 * 监测VCO频率偏差，确保频率稳定
 */
class FrequencyDeviationMonitor {
private:
    // 监测参数
    float nominal_frequency = 50.0f;            // 标称频率
    float deviation_threshold = 0.5f;           // 偏差阈值
    float critical_deviation = 1.0f;            // 临界偏差
    
    // 频率监测
    float current_frequency = 50.0f;            // 当前频率
    float frequency_deviation = 0.0f;           // 频率偏差
    bool deviation_detected = false;            // 偏差检测标志
    
    // 统计数据
    float max_deviation = 0.0f;                 // 最大偏差
    int deviation_events = 0;                   // 偏差事件数
    
public:
    FrequencyDeviationMonitor();
    
    void update(float vco_frequency);
    bool isDeviationDetected();
    float getDeviation();
    void reset();
    void setThresholds(float normal, float critical);
    void printStatus();
};

/**
 * 系统过载检测器
 * 检测系统过载情况，防止系统崩溃
 */
class SystemOverloadDetector {
private:
    // 过载参数
    int max_updates_per_second = 1000;          // 最大更新频率
    int max_corrections_per_minute = 60;        // 最大校正频率
    float cpu_usage_threshold = 80.0f;          // CPU使用率阈值
    
    // 监测数据
    int updates_count = 0;                      // 更新计数
    int corrections_count = 0;                  // 校正计数
    unsigned long last_reset_time = 0;          // 上次重置时间
    bool overload_detected = false;             // 过载检测标志
    
    // 性能统计
    float avg_update_interval = 0.0f;           // 平均更新间隔
    float max_update_interval = 0.0f;           // 最大更新间隔
    
public:
    SystemOverloadDetector();
    
    void recordUpdate();
    void recordCorrection();
    bool isOverloadDetected();
    void reset();
    void setLimits(int max_updates, int max_corrections);
    void printStatus();
    
private:
    void checkOverload();
    void updateStatistics();
};

/**
 * 综合保护控制器
 * 统一管理所有保护机制，提供分级保护
 */
class ProtectionController {
private:
    // 保护组件
    PhaseLossDetector* phase_detector;
    PowerDriftMonitor* drift_monitor;
    FrequencyDeviationMonitor* freq_monitor;
    SystemOverloadDetector* overload_detector;
    
    // 保护状态
    ProtectionLevel protection_level = PROTECTION_ENHANCED;
    ProtectionStatus current_status = PROTECTION_NORMAL;
    FaultType active_fault = FAULT_NONE;
    
    // 保护动作
    bool emergency_shutdown_enabled = true;     // 紧急关闭使能
    bool auto_recovery_enabled = true;          // 自动恢复使能
    int max_recovery_attempts = 3;              // 最大恢复尝试次数
    
    // 保护统计
    int protection_activations = 0;             // 保护激活次数
    int successful_recoveries = 0;              // 成功恢复次数
    int emergency_shutdowns = 0;                // 紧急关闭次数
    
    // 时间管理
    unsigned long last_protection_check = 0;    // 上次保护检查时间
    unsigned long protection_check_interval = 100; // 保护检查间隔
    
public:
    ProtectionController();
    ~ProtectionController();
    
    // 初始化保护系统
    bool begin();
    
    // 主更新函数
    void update(DigitalPLLControl* pll, PowerFeedbackMonitor* monitor);
    
    // 保护级别控制
    void setProtectionLevel(ProtectionLevel level);
    ProtectionLevel getProtectionLevel();
    
    // 状态查询
    ProtectionStatus getStatus();
    FaultType getActiveFault();
    bool isProtectionActive();
    bool isEmergencyShutdown();
    
    // 保护动作
    void triggerEmergencyShutdown();
    void attemptRecovery();
    void clearFaults();
    void resetProtection();
    
    // 配置接口
    void enableEmergencyShutdown(bool enable);
    void enableAutoRecovery(bool enable);
    void setMaxRecoveryAttempts(int attempts);
    
    // 诊断和统计
    void printProtectionStatus();
    void printDetailedReport();
    float getProtectionEffectiveness();
    
private:
    // 保护逻辑
    void checkProtectionConditions(DigitalPLLControl* pll, PowerFeedbackMonitor* monitor);
    void evaluateProtectionLevel();
    void executeProtectionActions();
    
    // 故障处理
    void handlePhaseLossFault();
    void handlePowerDriftFault();
    void handleFrequencyDeviationFault();
    void handleSystemOverloadFault();
    
    // 恢复逻辑
    bool attemptAutomaticRecovery();
    void executeRecoverySequence();
    
    // 统计更新
    void updateProtectionStatistics();
};

#endif // PLL_PROTECTION_SYSTEM_H
