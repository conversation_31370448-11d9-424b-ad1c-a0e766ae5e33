// optimized_pwm_control.cpp
// 针对无过零检测的可控硅PWM控制优化实现
#include "optimized_pwm_control.h"
#include <math.h>

OptimizedPWMControl::OptimizedPWMControl(int channel) {
    pwm_channel = channel;

    // 默认配置为1600W发热丝
    heater_max_watts = 1600.0f;
    heater_type = 1600;

    initializePWMLinearizationTable();
}

void OptimizedPWMControl::begin() {
    // PWM配置在主程序的setup()中已经完成，这里只需要初始化内部状态
    // 不重复配置PWM，避免API兼容性问题
    
    Serial.println("[OPTIMIZED_PWM] 优化PWM控制已初始化");
    Serial.printf("[OPTIMIZED_PWM] PWM频率: %dHz, 分辨率: %d位\n", pwm_frequency, pwm_resolution);
    Serial.println("[OPTIMIZED_PWM] 特性: 高频PWM + 增强平滑 + 变化率限制");
    Serial.println("[OPTIMIZED_PWM] 注意: PWM通道配置由主程序管理");
}

void OptimizedPWMControl::initializePWMLinearizationTable() {
    // 自适应发热丝功率的可控硅特性补偿算法
    Serial.printf("[OPTIMIZED_PWM] 初始化%.0fW发热丝可控硅特性补偿算法...\n", heater_max_watts);

    // 根据发热丝功率调整参数
    float power_factor = heater_max_watts / 1200.0f;  // 以1200W为基准
    int dead_zone = 8;  // 基础死区

    // 根据功率调整死区和压制强度
    if (heater_max_watts >= 1600.0f) {
        dead_zone = 8;  // 高功率发热丝需要更大死区
    } else if (heater_max_watts >= 1200.0f) {
        dead_zone = 6;
    } else {
        dead_zone = 4;  // 低功率发热丝死区较小
    }

    for (int i = 0; i <= 100; i++) {
        if (i == 0) {
            pwm_linearization_table[i] = 0.0f;
        } else {
            float input_ratio = i / 100.0f;

            // 自适应算法：根据发热丝功率调整压制强度
            if (i < dead_zone) {
                // 动态死区
                pwm_linearization_table[i] = 0.0f;
            } else if (i <= dead_zone + 10) {
                // 极度压制区域：功率越大压制越强
                float range = (i - dead_zone) / 10.0f;
                float max_power_ratio = 0.15f + (0.05f / power_factor);  // 功率越大压制越强
                pwm_linearization_table[i] = range * max_power_ratio;
            } else if (i <= dead_zone + 25) {
                // 缓慢增长区域
                float range = (i - dead_zone - 10) / 15.0f;
                float base_power = 0.15f + (0.05f / power_factor);
                pwm_linearization_table[i] = base_power + range * 0.25f;
            } else if (i <= 60) {
                // 中等增长区域
                float range = (i - dead_zone - 25) / (35 - dead_zone);
                pwm_linearization_table[i] = 0.40f + range * 0.25f;
            } else if (i <= 85) {
                // 较快增长区域
                float range = (i - 60.0f) / 25.0f;
                pwm_linearization_table[i] = 0.65f + range * 0.20f;
            } else {
                // 最终增长到满功率
                float range = (i - 85.0f) / 15.0f;
                pwm_linearization_table[i] = 0.85f + range * 0.15f;
            }
        }
    }

    Serial.printf("[OPTIMIZED_PWM] %.0fW发热丝特性补偿算法已初始化\n", heater_max_watts);
    Serial.printf("[OPTIMIZED_PWM] 死区: %d%%, 功率因子: %.2f\n", dead_zone, power_factor);
    Serial.printf("[OPTIMIZED_PWM] %.0fW优化算法功率映射：\n", heater_max_watts);

    // 显示关键测试点
    int key_points[] = {dead_zone, dead_zone+2, dead_zone+4, dead_zone+6, dead_zone+8, dead_zone+10, 20, 25, 30, 40, 50, 70, 100};
    int num_points = sizeof(key_points) / sizeof(key_points[0]);

    for (int j = 0; j < num_points; j++) {
        int i = key_points[j];
        if (i <= 100) {
            float coeff = pwm_linearization_table[i];
            int pwm_val = (int)(coeff * 4095);
            float expected_watts = heater_max_watts * coeff;

            Serial.printf("  %2d%% -> %.3f -> PWM:%4d -> 预期功率:%4.0fW\n",
                          i, coeff, pwm_val, expected_watts);
        }
    }

    Serial.printf("[OPTIMIZED_PWM] %.0fW发热丝压制目标：\n", heater_max_watts);
    Serial.printf("[OPTIMIZED_PWM] - %d%%以下：死区保护\n", dead_zone);
    Serial.printf("[OPTIMIZED_PWM] - %d%%：约%.0fW（安全启动）\n", dead_zone+2, heater_max_watts * pwm_linearization_table[dead_zone+2]);
    Serial.printf("[OPTIMIZED_PWM] - %d%%：约%.0fW（稳定控制）\n", dead_zone+4, heater_max_watts * pwm_linearization_table[dead_zone+4]);
    Serial.printf("[OPTIMIZED_PWM] - %d%%：约%.0fW（正常使用）\n", dead_zone+6, heater_max_watts * pwm_linearization_table[dead_zone+6]);
    Serial.printf("[OPTIMIZED_PWM] - 20%%：约%.0fW（中等功率）\n", heater_max_watts * pwm_linearization_table[20]);
}

void OptimizedPWMControl::setPower(int power_percent) {
    power_percent = constrain(power_percent, 0, 100);

    // 安全模式检查
    if (safety_mode_enabled) {
        int max_safe_percent = (int)(max_safe_power_ratio * 100);
        if (power_percent > max_safe_percent) {
            Serial.printf("[OPTIMIZED_PWM] 安全保护：功率 %d%% 超过安全限制 %d%%，自动限制\n",
                         power_percent, max_safe_percent);
            power_percent = max_safe_percent;
        }
    }

    // 应用增强的功率平滑
    int smoothed_power = enhancedPowerSmoothing(power_percent);

    // 应用功率变化率限制
    int limited_power = limitPowerChangeRate(smoothed_power);

    target_power = limited_power;

    // 详细调试信息，特别关注低功率区域
    float linear_coeff = linearizePWMPower(target_power);
    int pwm_value = calculatePWMValue();
    float actual_watts = heater_max_watts * linear_coeff;

    Serial.printf("[OPTIMIZED_PWM] 功率设置: %d%% -> 平滑: %d%% -> 限制: %d%%\n",
                  power_percent, smoothed_power, limited_power);
    Serial.printf("[OPTIMIZED_PWM] 线性化: %.3f -> PWM值: %d (%.1f%%) -> 实际功率: %.0fW\n",
                  linear_coeff, pwm_value, (pwm_value / 4095.0f) * 100, actual_watts);
}

int OptimizedPWMControl::getCurrentPower() {
    return target_power;
}

void OptimizedPWMControl::update() {
    int pwm_value = calculatePWMValue();
    
    // 使用主程序中已有的PWM写入方式，确保兼容性
    ledcWriteChannel(pwm_channel, pwm_value);
}

void OptimizedPWMControl::setPWMFrequency(int frequency) {
    if (frequency >= 1000 && frequency <= 20000) {
        pwm_frequency = frequency;
        
        // 动态频率调整需要重新配置PWM通道
        // 使用主程序中相同的API
        ledcAttachChannel(OUT_FIR, pwm_frequency, pwm_resolution, pwm_channel);
        
        Serial.printf("[OPTIMIZED_PWM] PWM频率更新为: %dHz\n", pwm_frequency);
        Serial.println("[OPTIMIZED_PWM] 频率已动态更新");
    } else {
        Serial.println("[OPTIMIZED_PWM] 错误：PWM频率应在1000-20000Hz范围内");
    }
}

void OptimizedPWMControl::setSmoothingFactor(float factor) {
    smoothing_factor = constrain(factor, 0.05f, 0.5f);
    Serial.printf("[OPTIMIZED_PWM] 平滑因子设置为: %.3f\n", smoothing_factor);
}

void OptimizedPWMControl::setPowerChangeLimit(int max_change) {
    max_power_change_per_cycle = constrain(max_change, 1, 10);
    Serial.printf("[OPTIMIZED_PWM] 功率变化率限制: %d%%/周期\n", max_power_change_per_cycle);
}

float OptimizedPWMControl::linearizePWMPower(int power_percent) {
    power_percent = constrain(power_percent, 0, 100);
    return pwm_linearization_table[power_percent];
}

int OptimizedPWMControl::enhancedPowerSmoothing(int new_power) {
    // 临时禁用复杂平滑算法，确保线性度
    // 只使用简单的3点移动平均，避免过度平滑
    
    static int simple_buffer[3] = {0, 0, 0};
    static int simple_index = 0;
    
    simple_buffer[simple_index] = new_power;
    simple_index = (simple_index + 1) % 3;
    
    int sum = simple_buffer[0] + simple_buffer[1] + simple_buffer[2];
    return sum / 3;
}

int OptimizedPWMControl::limitPowerChangeRate(int new_power) {
    // 临时禁用功率变化率限制，确保响应准确性
    // 直接返回输入值，不做限制
    last_output_power = new_power;
    return new_power;
}

int OptimizedPWMControl::calculatePWMValue() {
    if (target_power <= 0) {
        return 0;
    }
    
    if (target_power >= 100) {
        return 4095;
    }
    
    // 使用优化的线性化功率计算PWM值
    float linear_power = linearizePWMPower(target_power);
    return (int)(linear_power * 4095);
}

void OptimizedPWMControl::reset() {
    target_power = 0;
    last_output_power = 0;
    
    // 清空功率缓冲区
    for (int i = 0; i < 15; i++) {
        power_buffer[i] = 0;
    }
    buffer_index = 0;
    
    Serial.println("[OPTIMIZED_PWM] PWM控制器已重置");
}

void OptimizedPWMControl::printDebugInfo() {
    Serial.println("========== 优化PWM控制调试信息 ==========");
    Serial.printf("发热丝功率: %.0fW (类型:%d)\n", heater_max_watts, heater_type);
    Serial.printf("目标功率: %d%%\n", target_power);

    float linear_coeff = linearizePWMPower(target_power);
    int pwm_value = calculatePWMValue();
    float actual_watts = heater_max_watts * linear_coeff;

    Serial.printf("线性化系数: %.3f\n", linear_coeff);
    Serial.printf("实际功率: %.0fW (%.1f%%)\n", actual_watts, linear_coeff * 100);
    Serial.printf("PWM频率: %dHz\n", pwm_frequency);
    Serial.printf("PWM值: %d (%.1f%%占空比)\n", pwm_value, (pwm_value / 4095.0f) * 100);
    Serial.printf("平滑因子: %.3f\n", smoothing_factor);
    Serial.printf("变化率限制: %d%%/周期\n", max_power_change_per_cycle);
    Serial.printf("安全模式: %s", safety_mode_enabled ? "启用" : "禁用");

    if (safety_mode_enabled) {
        Serial.printf(" (最大%.1f%%)", max_safe_power_ratio * 100);
    }
    Serial.println();

    Serial.println("==========================================");
}

// 添加线性化表验证函数
void OptimizedPWMControl::printLinearizationTable() {
    Serial.println("========== PWM线性化查找表 ==========");
    Serial.println("功率% | 线性化系数 | PWM值 | 占空比%");
    for (int i = 0; i <= 100; i += 5) {
        float coeff = pwm_linearization_table[i];
        int pwm_val = (int)(coeff * 4095);
        float duty_cycle = coeff * 100;
        Serial.printf("%3d%% | %8.3f | %4d | %6.1f%%\n", 
                      i, coeff, pwm_val, duty_cycle);
    }
    Serial.println("=====================================");
}

// OptimizedPowerStabilityMonitor 实现
void OptimizedPowerStabilityMonitor::addPowerData(float power) {
    power_history[history_index] = power;
    history_index = (history_index + 1) % 30;
}

bool OptimizedPowerStabilityMonitor::isPowerStable() {
    float variation = getPowerVariation();
    return variation < stability_threshold;
}

float OptimizedPowerStabilityMonitor::getPowerVariation() {
    float sum = 0;
    float mean = getAveragePower();
    
    // 计算标准差
    float variance = 0;
    for (int i = 0; i < 30; i++) {
        variance += pow(power_history[i] - mean, 2);
    }
    
    return sqrt(variance / 30.0f);
}

float OptimizedPowerStabilityMonitor::getAveragePower() {
    float sum = 0;
    for (int i = 0; i < 30; i++) {
        sum += power_history[i];
    }
    return sum / 30.0f;
}

void OptimizedPowerStabilityMonitor::reset() {
    for (int i = 0; i < 30; i++) {
        power_history[i] = 0;
    }
    history_index = 0;
}

// ============================================================================
// 发热丝配置接口实现
// ============================================================================

void OptimizedPWMControl::setHeaterWattage(float watts) {
    if (watts >= 800.0f && watts <= 3000.0f) {  // 支持800W-3000W范围
        heater_max_watts = watts;
        heater_type = (int)watts;

        // 重新初始化线性化表
        initializePWMLinearizationTable();

        Serial.printf("[OPTIMIZED_PWM] 发热丝功率设置为: %.0fW\n", watts);
        Serial.println("[OPTIMIZED_PWM] 线性化表已重新计算");
    } else {
        Serial.printf("[OPTIMIZED_PWM] 错误：发热丝功率 %.0fW 超出支持范围(800-3000W)\n", watts);
    }
}

void OptimizedPWMControl::setHeaterType(int type) {
    switch (type) {
        case 800:
            setHeaterWattage(800.0f);
            break;
        case 1000:
            setHeaterWattage(1000.0f);
            break;
        case 1200:
            setHeaterWattage(1200.0f);
            break;
        case 1600:
            setHeaterWattage(1600.0f);
            break;
        case 2000:
            setHeaterWattage(2000.0f);
            break;
        default:
            Serial.printf("[OPTIMIZED_PWM] 警告：未知发热丝类型 %dW，使用自定义功率\n", type);
            setHeaterWattage((float)type);
            break;
    }
}

float OptimizedPWMControl::getHeaterWattage() {
    return heater_max_watts;
}

int OptimizedPWMControl::getHeaterType() {
    return heater_type;
}

// ============================================================================
// 安全保护接口实现
// ============================================================================

void OptimizedPWMControl::setSafetyMode(bool enabled) {
    safety_mode_enabled = enabled;
    Serial.printf("[OPTIMIZED_PWM] 安全模式: %s\n", enabled ? "启用" : "禁用");

    if (enabled) {
        Serial.printf("[OPTIMIZED_PWM] 最大安全功率: %.0fW (%.1f%%)\n",
                     heater_max_watts * max_safe_power_ratio, max_safe_power_ratio * 100);
    }
}

void OptimizedPWMControl::setMaxSafePowerRatio(float ratio) {
    if (ratio >= 0.5f && ratio <= 1.0f) {
        max_safe_power_ratio = ratio;
        Serial.printf("[OPTIMIZED_PWM] 最大安全功率比例设置为: %.1f%% (%.0fW)\n",
                     ratio * 100, heater_max_watts * ratio);
    } else {
        Serial.printf("[OPTIMIZED_PWM] 错误：安全功率比例 %.1f%% 超出范围(50%-100%%)\n", ratio * 100);
    }
}

bool OptimizedPWMControl::isSafetyModeEnabled() {
    return safety_mode_enabled;
}

float OptimizedPWMControl::getMaxSafePowerRatio() {
    return max_safe_power_ratio;
}

// ============================================================================
// 发热丝配置信息打印
// ============================================================================

void OptimizedPWMControl::printHeaterConfig() {
    Serial.println("========== 发热丝配置信息 ==========");
    Serial.printf("发热丝功率: %.0fW\n", heater_max_watts);
    Serial.printf("发热丝类型: %d\n", heater_type);
    Serial.printf("安全模式: %s\n", safety_mode_enabled ? "启用" : "禁用");

    if (safety_mode_enabled) {
        Serial.printf("最大安全功率: %.1f%% (%.0fW)\n",
                     max_safe_power_ratio * 100, heater_max_watts * max_safe_power_ratio);
    }

    Serial.printf("PWM频率: %dHz\n", pwm_frequency);
    Serial.printf("PWM分辨率: %d位\n", pwm_resolution);
    Serial.printf("平滑因子: %.3f\n", smoothing_factor);
    Serial.printf("功率变化限制: %d%%/周期\n", max_power_change_per_cycle);

    // 显示关键功率点的实际输出
    Serial.println("\n关键功率点预览:");
    int test_points[] = {10, 15, 20, 25, 30, 40, 50};
    int num_test_points = sizeof(test_points) / sizeof(test_points[0]);

    for (int i = 0; i < num_test_points; i++) {
        int power = test_points[i];
        float coeff = pwm_linearization_table[power];
        float actual_watts = heater_max_watts * coeff;
        Serial.printf("  设置%d%% -> 实际%.0fW (%.1f%%实际功率)\n",
                     power, actual_watts, coeff * 100);
    }

    Serial.println("=====================================");
}

// ============================================================================
// 增强的调试信息函数
// ============================================================================