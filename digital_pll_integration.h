// digital_pll_integration.h
// 数字锁相环系统集成 - 替换现有相位控制
#ifndef DIGITAL_PLL_INTEGRATION_H
#define DIGITAL_PLL_INTEGRATION_H

#include <Arduino.h>
#include "digital_pll_control.h"
#include "power_feedback_monitor.h"

// 集成配置
#define USE_DIGITAL_PLL                   // 启用数字锁相环
#define PLL_INTEGRATION_DEBUG             // 启用调试输出
#define PLL_AUTO_RECOVERY                 // 启用自动恢复
#define PLL_POWER_FEEDBACK                // 启用功率反馈

// 系统状态
enum PLLSystemStatus {
    PLL_SYSTEM_INITIALIZING,              // 初始化中
    PLL_SYSTEM_NORMAL,                    // 正常运行
    PLL_SYSTEM_DRIFT_DETECTED,            // 检测到漂移
    PLL_SYSTEM_PHASE_LOSS,                // 相位失联
    PLL_SYSTEM_RECOVERY,                  // 恢复中
    PLL_SYSTEM_ERROR                      // 系统错误
};

/**
 * 数字锁相环集成控制器
 * 统一管理数字锁相环、功率监测和校正系统
 */
class DigitalPLLIntegration {
private:
    // 核心组件
    DigitalPLLControl* pll_controller;
    PowerFeedbackMonitor* feedback_monitor;
    PowerCorrectionController* correction_controller;
    PLLDiagnostics* diagnostics;
    
    // 系统状态
    PLLSystemStatus system_status = PLL_SYSTEM_INITIALIZING;
    bool system_enabled = true;
    bool auto_recovery_enabled = true;
    
    // 性能监测
    unsigned long last_status_update = 0;
    unsigned long last_diagnostic_report = 0;
    unsigned long system_start_time = 0;
    
    // 统计数据
    int total_power_adjustments = 0;
    int successful_recoveries = 0;
    int failed_recoveries = 0;
    float avg_stability_score = 0.0f;
    
    // 配置参数
    bool debug_enabled = true;
    int status_update_interval = 1000;    // 状态更新间隔1秒
    int diagnostic_interval = 30000;      // 诊断报告间隔30秒
    
public:
    DigitalPLLIntegration();
    ~DigitalPLLIntegration();
    
    // 初始化系统
    bool begin(int pwm_channel = 5);
    
    // 主更新函数 - 替换原有的相位控制更新
    void update();
    
    // 功率控制接口 - 替换原有的setPower函数
    void setPower(int power_percent);
    int getCurrentPower();
    
    // 系统状态查询
    PLLSystemStatus getSystemStatus();
    bool isSystemStable();
    bool isPLLLocked();
    bool isPhaseConnected();
    
    // 诊断和监测
    float getStabilityScore();
    float getPowerVariance();
    float getVCOFrequency();
    void printSystemStatus();
    void printDetailedDiagnostics();
    
    // 控制功能
    void enableSystem(bool enable);
    void enableAutoRecovery(bool enable);
    void enableDebug(bool enable);
    void resetSystem();
    void forceRecovery();
    
    // 配置接口
    void setStatusUpdateInterval(int interval_ms);
    void setDiagnosticInterval(int interval_ms);
    void setPLLParameters(float kp, float ki, float kd);
    void setCorrectionGain(float gain);
    
    // 兼容性接口 - 与现有代码兼容
    void begin_compatibility();
    void update_compatibility();
    void setPower_compatibility(int power_percent);
    int getCurrentPower_compatibility();
    void printDebugInfo_compatibility();
    
private:
    // 系统管理
    void updateSystemStatus();
    void handleSystemState();
    void performDiagnostics();
    
    // 状态处理
    void handleNormalOperation();
    void handleDriftDetection();
    void handlePhaseLoss();
    void handleRecovery();
    void handleSystemError();
    
    // 自动恢复
    bool attemptAutoRecovery();
    void executeRecoverySequence();
    
    // 统计更新
    void updateStatistics();
    
    // 调试输出
    void debugPrint(const char* message);
    void debugPrintf(const char* format, ...);
};

/**
 * 全局集成接口
 * 提供简单的全局函数接口，便于现有代码集成
 */
class GlobalPLLInterface {
private:
    static DigitalPLLIntegration* instance;
    
public:
    // 初始化全局接口
    static bool initialize(int pwm_channel = 5);
    
    // 获取实例
    static DigitalPLLIntegration* getInstance();
    
    // 全局函数接口
    static void updatePLL();
    static void setPowerPLL(int power_percent);
    static int getCurrentPowerPLL();
    static bool isPLLStable();
    static void printPLLStatus();
    static void resetPLL();
    
    // 清理资源
    static void cleanup();
};

// 便捷宏定义 - 用于快速替换现有代码
#ifdef USE_DIGITAL_PLL

// 替换现有的相位控制调用
#define PHASE_CONTROL_UPDATE()           GlobalPLLInterface::updatePLL()
#define PHASE_CONTROL_SET_POWER(power)   GlobalPLLInterface::setPowerPLL(power)
#define PHASE_CONTROL_GET_POWER()        GlobalPLLInterface::getCurrentPowerPLL()
#define PHASE_CONTROL_IS_STABLE()        GlobalPLLInterface::isPLLStable()
#define PHASE_CONTROL_PRINT_STATUS()     GlobalPLLInterface::printPLLStatus()
#define PHASE_CONTROL_RESET()            GlobalPLLInterface::resetPLL()

// 初始化宏
#define INIT_DIGITAL_PLL(channel)        GlobalPLLInterface::initialize(channel)

#else

// 如果未启用数字锁相环，使用空宏
#define PHASE_CONTROL_UPDATE()           
#define PHASE_CONTROL_SET_POWER(power)   
#define PHASE_CONTROL_GET_POWER()        0
#define PHASE_CONTROL_IS_STABLE()        true
#define PHASE_CONTROL_PRINT_STATUS()     
#define PHASE_CONTROL_RESET()            
#define INIT_DIGITAL_PLL(channel)        true

#endif

// 测试和调试命令处理
void handlePLLTestCommand(String command);
void handlePLLDiagnosticCommand(String command);
void handlePLLConfigCommand(String command);

// 串口命令解析
void parsePLLCommand(String msg);

#endif // DIGITAL_PLL_INTEGRATION_H
