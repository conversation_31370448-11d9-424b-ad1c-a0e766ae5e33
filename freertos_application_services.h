#ifndef FREERTOS_APPLICATION_SERVICES_H
#define FREERTOS_APPLICATION_SERVICES_H

#include "freertos_architecture_design.h"

// ============================================================================
// FreeRTOS应用服务头文件
// ============================================================================

// 应用服务任务
void Task_File_Manager(void *pvParameters);
void Task_System_Monitor(void *pvParameters);

// 文件管理
typedef struct {
    char filename[64];
    uint32_t size;
    uint32_t created_time;
    bool is_recording;
} FileInfo_t;

// 文件操作
bool File_Init_System(void);
bool File_Create_New_Log(const char* filename);
bool File_Write_Data_Point(SensorData_t *data);
bool File_Close_Current_Log(void);
bool File_List_Available(FileInfo_t *files, uint8_t max_count);

// 系统监控
typedef struct {
    uint32_t free_heap;
    uint32_t min_free_heap;
    uint8_t cpu_usage;
    uint32_t uptime_seconds;
    uint8_t task_count;
} SystemStats_t;

// 监控函数
void Monitor_Update_Stats(void);
void Monitor_Check_Memory_Usage(void);
void Monitor_Check_Task_Health(void);
void Monitor_Print_System_Info(void);

// 看门狗和心跳
void Watchdog_Feed(void);
void Heartbeat_Update(void);
bool System_Health_Check(void);

// 定时器回调
void Timer_Watchdog_Callback(TimerHandle_t xTimer);
void Timer_Heartbeat_Callback(TimerHandle_t xTimer);

#endif // FREERTOS_APPLICATION_SERVICES_H
