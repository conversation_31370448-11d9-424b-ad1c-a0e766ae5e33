#ifndef FREERTOS_COMMUNICATION_H
#define FREERTOS_COMMUNICATION_H

#include "freertos_architecture_design.h"

// ============================================================================
// FreeRTOS通信接口头文件
// ============================================================================

// 通信任务
void Task_Serial_Comm(void *pvParameters);
void Task_BLE_Comm(void *pvParameters);
void Task_Display_Manager(void *pvParameters);

// 通信状态
typedef struct {
    bool serial_connected;
    bool ble_connected;
    uint32_t serial_commands_processed;
    uint32_t ble_commands_processed;
    uint32_t last_serial_activity;
    uint32_t last_ble_activity;
} CommStatus_t;

// 串口通信
void process_serial_command(const String& command);
void send_json_status_serial(void);
void send_data_notification_serial(void);

// BLE通信
void process_ble_command(const String& command);
void send_welcome_message_ble(void);
void send_json_status_ble(void);
void send_data_notification_ble(void);

// 显示管理
void update_display_data(void);
void display_system_status(void);
void display_sensor_data(void);
void display_error_message(const char* message);

// 数据格式化
String format_sensor_data_json(SensorData_t *data);
String format_system_status_json(void);

#endif // FREERTOS_COMMUNICATION_H
