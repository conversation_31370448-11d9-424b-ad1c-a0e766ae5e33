// freertos_application_services.cpp
// FreeRTOS应用服务层实现 - 数据记录和文件管理
#include "freertos_architecture_design.h"
#include "freertos_missing_definitions.h"
#include <SPIFFS.h>
#include <SD.h>
#include <ArduinoJson.h>
#include <FS.h>
#include <SPIFFS.h>
#include <ArduinoJson.h>
#include <SD.h>

// ============================================================================
// 应用服务层配置
// ============================================================================

// 文件系统配置
#define SPIFFS_FORMAT_IF_FAILED true
#define SD_CS_PIN               5
#define MAX_LOG_FILE_SIZE       (1024 * 1024)  // 1MB
#define MAX_LOG_FILES           10
#define CONFIG_FILE_PATH        "/config.json"
#define ROAST_DATA_PATH         "/roast_data"

// 数据记录配置
#define LOG_BUFFER_SIZE         100
#define BACKUP_INTERVAL_MS      (5 * 60 * 1000)  // 5分钟备份
#define CLEANUP_INTERVAL_MS     (24 * 60 * 60 * 1000)  // 24小时清理

// 使用全局定义的结构体，不重复定义

// ============================================================================
// 数据记录任务实现
// ============================================================================

void Task_Data_Record(void *pvParameters) {
    TickType_t xLastWakeTime = xTaskGetTickCount();
    LogData_t log_data;
    
    Serial.println("[RECORD] 数据记录任务启动 - 优先级0, 1Hz");
    
    // 初始化数据记录
    if (!init_data_recording()) {
        Serial.println("[RECORD] ❌ 数据记录初始化失败");
        vTaskDelete(NULL);
        return;
    }
    
    // 数据记录缓冲区
    LogData_t log_buffer[LOG_BUFFER_SIZE];
    uint16_t buffer_index = 0;
    uint32_t records_written = 0;
    uint32_t last_backup_time = 0;
    
    while(1) {
        // 1. 获取日志数据
        if (xQueueReceive(xQueueLogData, &log_data, pdMS_TO_TICKS(100)) == pdTRUE) {
            
            // 2. 添加到缓冲区
            log_buffer[buffer_index] = log_data;
            buffer_index++;
            
            // 3. 检查是否需要写入文件
            if (buffer_index >= LOG_BUFFER_SIZE || 
                (buffer_index > 0 && xTaskGetTickCount() - last_backup_time > pdMS_TO_TICKS(BACKUP_INTERVAL_MS))) {
                
                // 写入数据到文件
                if (write_log_buffer_to_file(log_buffer, buffer_index)) {
                    records_written += buffer_index;
                    buffer_index = 0;
                    last_backup_time = xTaskGetTickCount();
                    
                    // 更新烘焙会话信息
                    update_roast_session(&log_data);
                } else {
                    Serial.println("[RECORD] ⚠️ 数据写入失败");
                }
            }
        }
        
        // 4. 定期文件系统维护
        static uint32_t last_cleanup = 0;
        if (xTaskGetTickCount() - last_cleanup > pdMS_TO_TICKS(CLEANUP_INTERVAL_MS)) {
            last_cleanup = xTaskGetTickCount();
            perform_file_cleanup();
        }
        
        // 5. 监控文件系统状态
        static uint32_t last_fs_check = 0;
        if (xTaskGetTickCount() - last_fs_check > pdMS_TO_TICKS(60000)) { // 每分钟检查
            last_fs_check = xTaskGetTickCount();
            update_filesystem_status();
        }
        
        // 6. 状态报告
        static uint32_t last_report = 0;
        if (xTaskGetTickCount() - last_report > pdMS_TO_TICKS(300000)) { // 每5分钟
            last_report = xTaskGetTickCount();
            Serial.printf("[RECORD] 记录: %lu条, 缓冲: %d条, 会话: %s\n",
                         records_written, buffer_index, 
                         current_roast_session.active ? "活动" : "非活动");
        }
        
        vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(CYCLE_DATA_RECORD));
    }
}

bool init_data_recording(void) {
    Serial.println("[RECORD] 初始化数据记录系统...");
    
    // 1. 初始化SPIFFS
    if (SPIFFS.begin(SPIFFS_FORMAT_IF_FAILED)) {
        fs_status.spiffs_mounted = true;
        fs_status.spiffs_total = SPIFFS.totalBytes();
        fs_status.spiffs_used = SPIFFS.usedBytes();
        Serial.printf("[RECORD] ✅ SPIFFS挂载成功: %d/%d bytes\n", 
                     fs_status.spiffs_used, fs_status.spiffs_total);
    } else {
        Serial.println("[RECORD] ❌ SPIFFS挂载失败");
        return false;
    }
    
    // 2. 尝试初始化SD卡
    if (SD.begin(SD_CS_PIN)) {
        fs_status.sd_card_available = true;
        fs_status.sd_total = SD.totalBytes();
        fs_status.sd_used = SD.usedBytes();
        Serial.printf("[RECORD] ✅ SD卡可用: %d/%d bytes\n", 
                     fs_status.sd_used, fs_status.sd_total);
        xEventGroupSetBits(xEventGroupSystem, EVENT_BIT_SD_CARD_READY);
    } else {
        Serial.println("[RECORD] ⚠️ SD卡不可用，使用SPIFFS");
    }
    
    // 3. 创建必要的目录
    if (!SPIFFS.exists(ROAST_DATA_PATH)) {
        SPIFFS.mkdir(ROAST_DATA_PATH);
    }
    
    // 4. 初始化烘焙会话
    memset(&current_roast_session, 0, sizeof(RoastSession));
    current_roast_session.session_id = generate_session_id();
    
    Serial.println("[RECORD] ✅ 数据记录系统初始化完成");
    return true;
}

bool write_log_buffer_to_file(const LogData_t* buffer, uint16_t count) {
    if (count == 0) return true;
    
    // 生成文件名
    char filename[64];
    snprintf(filename, sizeof(filename), "%s/roast_%lu.csv", 
             ROAST_DATA_PATH, current_roast_session.session_id);
    
    // 选择存储位置（优先SD卡）
    fs::FS* filesystem = fs_status.sd_card_available ? (fs::FS*)&SD : (fs::FS*)&SPIFFS;
    
    // 打开文件（追加模式）
    File file = filesystem->open(filename, FILE_APPEND);
    if (!file) {
        Serial.printf("[RECORD] ❌ 无法打开文件: %s\n", filename);
        return false;
    }
    
    // 写入CSV头部（仅在新文件时）
    if (file.size() == 0) {
        file.println("timestamp,bean_temp,env_temp,power_percent,pid_output,ror");
    }
    
    // 写入数据
    for (uint16_t i = 0; i < count; i++) {
        file.printf("%lu,%.2f,%.2f,%.1f,%.1f,%d\n",
                   buffer[i].timestamp,
                   buffer[i].bean_temp,
                   buffer[i].env_temp,
                   buffer[i].power_percent,
                   buffer[i].pid_output,
                   buffer[i].ror);
    }
    
    file.close();
    
    Serial.printf("[RECORD] ✅ 写入 %d 条记录到 %s\n", count, filename);
    return true;
}

void update_roast_session(const LogData_t* log_data) {
    // 检查是否开始新的烘焙会话
    EventBits_t events = xEventGroupGetBits(xEventGroupSystem);
    bool roast_active = (events & EVENT_BIT_ROAST_ACTIVE) != 0;
    
    if (roast_active && !current_roast_session.active) {
        // 开始新会话
        current_roast_session.active = true;
        current_roast_session.start_time = log_data->timestamp;
        current_roast_session.start_temp = log_data->bean_temp;
        current_roast_session.max_temp = log_data->bean_temp;
        current_roast_session.total_points = 1;
        strcpy(current_roast_session.profile_name, "Auto");
        
        Serial.printf("[RECORD] 🚀 开始烘焙会话: %lu\n", current_roast_session.session_id);
        
    } else if (!roast_active && current_roast_session.active) {
        // 结束会话
        current_roast_session.active = false;
        current_roast_session.end_time = log_data->timestamp;
        current_roast_session.end_temp = log_data->bean_temp;
        
        // 保存会话摘要
        save_roast_session_summary();
        
        Serial.printf("[RECORD] 🏁 结束烘焙会话: %lu, 持续: %lu秒\n", 
                     current_roast_session.session_id,
                     (current_roast_session.end_time - current_roast_session.start_time) / 1000);
        
        // 准备下一个会话
        current_roast_session.session_id = generate_session_id();
        
    } else if (current_roast_session.active) {
        // 更新会话数据
        current_roast_session.total_points++;
        if (log_data->bean_temp > current_roast_session.max_temp) {
            current_roast_session.max_temp = log_data->bean_temp;
        }
    }
}

uint32_t generate_session_id(void) {
    return (uint32_t)(xTaskGetTickCount() / 1000); // 简单的时间戳ID
}

void save_roast_session_summary(void) {
    char filename[64];
    snprintf(filename, sizeof(filename), "%s/session_%lu.json", 
             ROAST_DATA_PATH, current_roast_session.session_id);
    
    // 创建JSON文档
    DynamicJsonDocument doc(512);
    doc["session_id"] = current_roast_session.session_id;
    doc["start_time"] = current_roast_session.start_time;
    doc["end_time"] = current_roast_session.end_time;
    doc["duration"] = current_roast_session.end_time - current_roast_session.start_time;
    doc["start_temp"] = current_roast_session.start_temp;
    doc["end_temp"] = current_roast_session.end_temp;
    doc["max_temp"] = current_roast_session.max_temp;
    doc["total_points"] = current_roast_session.total_points;
    doc["profile_name"] = current_roast_session.profile_name;
    
    // 写入文件
    fs::FS* filesystem = fs_status.sd_card_available ? (fs::FS*)&SD : (fs::FS*)&SPIFFS;
    File file = filesystem->open(filename, FILE_WRITE);
    if (file) {
        serializeJson(doc, file);
        file.close();
        Serial.printf("[RECORD] ✅ 会话摘要已保存: %s\n", filename);
    } else {
        Serial.printf("[RECORD] ❌ 无法保存会话摘要: %s\n", filename);
    }
}

// ============================================================================
// 文件管理任务实现
// ============================================================================

void Task_File_Manager(void *pvParameters) {
    Serial.println("[FILE] 文件管理任务启动 - 优先级0, 事件驱动");
    
    while(1) {
        // 1. 等待文件操作请求
        // 这里可以通过队列接收文件操作命令
        
        // 2. 定期维护任务
        static uint32_t last_maintenance = 0;
        if (xTaskGetTickCount() - last_maintenance > pdMS_TO_TICKS(3600000)) { // 每小时
            last_maintenance = xTaskGetTickCount();
            perform_file_maintenance();
        }
        
        // 3. 配置文件监控
        static uint32_t last_config_check = 0;
        if (xTaskGetTickCount() - last_config_check > pdMS_TO_TICKS(60000)) { // 每分钟
            last_config_check = xTaskGetTickCount();
            check_config_file_changes();
        }
        
        vTaskDelay(pdMS_TO_TICKS(5000)); // 5秒延迟
    }
}

void perform_file_cleanup(void) {
    Serial.println("[RECORD] 执行文件清理...");
    
    // 1. 检查日志文件大小
    File root = SPIFFS.open(ROAST_DATA_PATH);
    if (root && root.isDirectory()) {
        File file = root.openNextFile();
        while (file) {
            if (file.size() > MAX_LOG_FILE_SIZE) {
                Serial.printf("[RECORD] 删除大文件: %s (%d bytes)\n", 
                             file.name(), file.size());
                SPIFFS.remove(file.name());
            }
            file = root.openNextFile();
        }
        root.close();
    }
    
    // 2. 限制文件数量
    // 这里可以添加文件数量限制逻辑
    
    Serial.println("[RECORD] ✅ 文件清理完成");
}

void update_filesystem_status(void) {
    if (fs_status.spiffs_mounted) {
        fs_status.spiffs_used = SPIFFS.usedBytes();
        
        // 检查空间不足
        float usage_percent = (float)fs_status.spiffs_used / fs_status.spiffs_total * 100.0f;
        if (usage_percent > 90.0f) {
            Serial.printf("[RECORD] ⚠️ SPIFFS空间不足: %.1f%%\n", usage_percent);
        }
    }
    
    if (fs_status.sd_card_available) {
        fs_status.sd_used = SD.usedBytes();
    }
}

void perform_file_maintenance(void) {
    Serial.println("[FILE] 执行文件系统维护...");
    
    // 1. 碎片整理（如果支持）
    // 2. 备份重要文件
    // 3. 检查文件完整性
    // 4. 更新索引
    
    Serial.println("[FILE] ✅ 文件系统维护完成");
}

void check_config_file_changes(void) {
    // 检查配置文件是否有变化
    // 如果有变化，重新加载配置
}

// ============================================================================
// 配置管理功能
// ============================================================================

bool save_system_config(void) {
    DynamicJsonDocument doc(1024);
    
    // 系统配置
    doc["version"] = fs_status.config_version;
    doc["device_name"] = "CoffeeRoaster_FreeRTOS";
    
    // PID参数
    JsonObject pid = doc.createNestedObject("pid");
    pid["kp"] = Kp;
    pid["ki"] = Ki;
    pid["kd"] = Kd;
    
    // 传感器配置
    JsonObject sensors = doc.createNestedObject("sensors");
    sensors["thermocouple_offset"] = sensor_config.thermocouple_offset;
    sensors["thermocouple_scale"] = sensor_config.thermocouple_scale;
    sensors["env_temp_offset"] = sensor_config.env_temp_offset;
    sensors["env_temp_scale"] = sensor_config.env_temp_scale;
    
    // 安全参数
    JsonObject safety = doc.createNestedObject("safety");
    safety["max_temp"] = 280.0f;
    safety["max_power"] = 85.0f;
    
    // 写入文件
    File file = SPIFFS.open(CONFIG_FILE_PATH, FILE_WRITE);
    if (file) {
        serializeJson(doc, file);
        file.close();
        Serial.println("[FILE] ✅ 系统配置已保存");
        return true;
    } else {
        Serial.println("[FILE] ❌ 无法保存系统配置");
        return false;
    }
}

bool load_system_config(void) {
    if (!SPIFFS.exists(CONFIG_FILE_PATH)) {
        Serial.println("[FILE] 配置文件不存在，使用默认配置");
        return save_system_config(); // 创建默认配置
    }
    
    File file = SPIFFS.open(CONFIG_FILE_PATH, FILE_READ);
    if (!file) {
        Serial.println("[FILE] ❌ 无法读取配置文件");
        return false;
    }
    
    DynamicJsonDocument doc(1024);
    DeserializationError error = deserializeJson(doc, file);
    file.close();
    
    if (error) {
        Serial.printf("[FILE] ❌ 配置文件解析错误: %s\n", error.c_str());
        return false;
    }
    
    // 加载PID参数
    if (doc.containsKey("pid")) {
        Kp = doc["pid"]["kp"] | Kp;
        Ki = doc["pid"]["ki"] | Ki;
        Kd = doc["pid"]["kd"] | Kd;
        Control_Set_PID_Parameters(Kp, Ki, Kd);
    }
    
    // 加载传感器配置
    if (doc.containsKey("sensors")) {
        sensor_config.thermocouple_offset = doc["sensors"]["thermocouple_offset"] | 0.0f;
        sensor_config.thermocouple_scale = doc["sensors"]["thermocouple_scale"] | 1.0f;
        sensor_config.env_temp_offset = doc["sensors"]["env_temp_offset"] | 0.0f;
        sensor_config.env_temp_scale = doc["sensors"]["env_temp_scale"] | 1.0f;
    }
    
    fs_status.config_version = doc["version"] | 1;
    
    Serial.println("[FILE] ✅ 系统配置已加载");
    return true;
}

// ============================================================================
// 应用服务层工具函数
// ============================================================================

void ApplicationServices_Print_Status(void) {
    Serial.println("========== 应用服务层状态 ==========");
    Serial.printf("SPIFFS: %s (%d/%d bytes)\n", 
                 fs_status.spiffs_mounted ? "已挂载" : "未挂载",
                 fs_status.spiffs_used, fs_status.spiffs_total);
    Serial.printf("SD卡: %s\n", fs_status.sd_card_available ? "可用" : "不可用");
    Serial.printf("当前会话: %lu (%s)\n", 
                 current_roast_session.session_id,
                 current_roast_session.active ? "活动" : "非活动");
    Serial.printf("日志队列: %d/%d\n", 
                 uxQueueMessagesWaiting(xQueueLogData), QUEUE_SIZE_LOG_DATA);
    Serial.printf("配置版本: %lu\n", fs_status.config_version);
    Serial.println("==================================");
}

bool ApplicationServices_Export_Data(const char* session_id) {
    // 导出指定会话的数据
    Serial.printf("[FILE] 导出会话数据: %s\n", session_id);
    // 实现数据导出逻辑
    return true;
}

bool ApplicationServices_Import_Profile(const char* profile_data) {
    // 导入烘焙配置文件
    Serial.printf("[FILE] 导入烘焙配置\n");
    // 实现配置导入逻辑
    return true;
}


