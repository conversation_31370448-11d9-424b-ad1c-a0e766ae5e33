// freertos_realtime_control.cpp
// FreeRTOS实时控制层实现 - 安全监控和PWM控制
#include "freertos_architecture_design.h"
#include "freertos_missing_definitions.h"
#include "optimized_pwm_control.h"

// ============================================================================
// 外部对象引用
// ============================================================================
extern OptimizedPWMControl pwmController;

// ============================================================================
// 实时控制层配置
// ============================================================================

// 安全阈值定义
#define MAX_SAFE_BEAN_TEMP          280.0f    // 最大安全豆温
#define MAX_SAFE_ENV_TEMP           100.0f    // 最大安全环温
#define MAX_POWER_VARIANCE          5.0f      // 最大功率方差
#define SENSOR_TIMEOUT_LIMIT        5000      // 传感器超时限制(ms)
#define TEMP_RISE_RATE_LIMIT        10.0f     // 温升速率限制(°C/s)

// PWM控制配置
#define PWM_UPDATE_TOLERANCE        0.5f      // PWM更新容差
#define PWM_SAFETY_RAMP_RATE        2.0f      // 安全斜坡速率(%/100ms)

// ============================================================================
// 安全监控任务实现
// ============================================================================

void Task_Safety_Monitor(void *pvParameters) {
    TickType_t xLastWakeTime = xTaskGetTickCount();
    SensorData_t sensor_data;
    
    // 安全监控状态变量
    float last_bean_temp = 0.0f;
    uint32_t last_sensor_update = 0;
    uint32_t consecutive_errors = 0;
    bool emergency_triggered = false;
    
    Serial.println("[SAFETY] 安全监控任务启动 - 优先级5, 50Hz");
    
    while(1) {
        // 1. 获取当前传感器数据
        bool data_valid = false;
        if (xSemaphoreTake(xMutexSharedData, pdMS_TO_TICKS(10)) == pdTRUE) {
            sensor_data = g_system_state.current_sensor_data;
            data_valid = sensor_data.valid;
            xSemaphoreGive(xMutexSharedData);
        }
        
        if (data_valid) {
            last_sensor_update = xTaskGetTickCount();
            consecutive_errors = 0;
            
            // 2. 温度安全检查
            if (sensor_data.bean_temperature > MAX_SAFE_BEAN_TEMP) {
                Serial.printf("[SAFETY] 🚨 豆温过高: %.1f°C > %.1f°C\n", 
                             sensor_data.bean_temperature, MAX_SAFE_BEAN_TEMP);
                System_Emergency_Stop();
                emergency_triggered = true;
            }
            
            if (sensor_data.env_temperature > MAX_SAFE_ENV_TEMP) {
                Serial.printf("[SAFETY] 🚨 环温过高: %.1f°C > %.1f°C\n", 
                             sensor_data.env_temperature, MAX_SAFE_ENV_TEMP);
                System_Emergency_Stop();
                emergency_triggered = true;
            }
            
            // 3. 温升速率检查
            if (last_bean_temp > 0) {
                float temp_rise_rate = (sensor_data.bean_temperature - last_bean_temp) * 50; // 50Hz转换为每秒
                if (temp_rise_rate > TEMP_RISE_RATE_LIMIT) {
                    Serial.printf("[SAFETY] ⚠️ 温升过快: %.2f°C/s > %.1f°C/s\n", 
                                 temp_rise_rate, TEMP_RISE_RATE_LIMIT);
                    // 降低功率而不是紧急停止
                    System_Set_PWM_Power(pwmController.getCurrentPower() * 0.8f);
                }
            }
            last_bean_temp = sensor_data.bean_temperature;
            
        } else {
            consecutive_errors++;
            
            // 4. 传感器超时检查
            uint32_t sensor_timeout = xTaskGetTickCount() - last_sensor_update;
            if (sensor_timeout > pdMS_TO_TICKS(SENSOR_TIMEOUT_LIMIT)) {
                Serial.printf("[SAFETY] 🚨 传感器超时: %lu ms\n", sensor_timeout);
                System_Emergency_Stop();
                emergency_triggered = true;
            }
            
            // 5. 连续错误检查
            if (consecutive_errors > 10) { // 连续10次错误(200ms)
                Serial.printf("[SAFETY] 🚨 传感器连续错误: %d次\n", consecutive_errors);
                System_Emergency_Stop();
                emergency_triggered = true;
            }
        }
        
        // 6. 功率方差检查
        if (pwmController.getCurrentPower() > 0) {
            // 这里可以添加功率方差检查逻辑
            // 需要从PWM控制器获取功率稳定性数据
        }
        
        // 7. 系统状态检查
        EventBits_t event_bits = xEventGroupGetBits(xEventGroupSystem);
        if (event_bits & EVENT_BIT_EMERGENCY_STOP) {
            if (!emergency_triggered) {
                Serial.println("[SAFETY] 🚨 外部紧急停止信号");
                emergency_triggered = true;
            }
            // 确保PWM输出为0
            System_Set_PWM_Power(0);
        } else {
            emergency_triggered = false;
        }
        
        // 8. 看门狗喂狗
        if (xTimerWatchdog) {
            xTimerReset(xTimerWatchdog, 0);
        }
        
        // 9. 更新安全统计
        static uint32_t safety_check_count = 0;
        safety_check_count++;
        if (safety_check_count % 2500 == 0) { // 每50秒报告一次
            Serial.printf("[SAFETY] 安全检查: %lu次, 错误: %lu次\n", 
                         safety_check_count, consecutive_errors);
        }
        
        // 10. 等待下一个周期
        vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(CYCLE_SAFETY_MONITOR));
    }
}

// ============================================================================
// PWM控制任务实现
// ============================================================================

void Task_PWM_Control(void *pvParameters) {
    TickType_t xLastWakeTime = xTaskGetTickCount();
    ControlCommand_t control_cmd;
    PWMControlData_t pwm_data;
    
    // PWM控制状态变量
    float current_target_power = 0.0f;
    float last_target_power = 0.0f;
    bool safety_override = false;
    uint32_t power_change_count = 0;
    
    Serial.println("[PWM] PWM控制任务启动 - 优先级4, 100Hz");
    
    // 初始化PWM数据
    pwm_data.target_power = 0.0f;
    pwm_data.actual_power = 0.0f;
    pwm_data.pwm_value = 0;
    pwm_data.safety_override = false;
    pwm_data.timestamp = xTaskGetTickCount();
    
    while(1) {
        // 1. 检查控制命令队列
        while (xQueueReceive(xQueueControlCmd, &control_cmd, 0) == pdTRUE) {
            if (control_cmd.command_type == CMD_SET_POWER) {
                float new_power = control_cmd.params.set_power.power_percent;
                
                // 功率范围检查
                new_power = constrain(new_power, 0.0f, 100.0f);
                
                // 安全斜坡限制
                float power_diff = abs(new_power - current_target_power);
                if (power_diff > PWM_SAFETY_RAMP_RATE) {
                    if (new_power > current_target_power) {
                        current_target_power += PWM_SAFETY_RAMP_RATE;
                    } else {
                        current_target_power -= PWM_SAFETY_RAMP_RATE;
                    }
                } else {
                    current_target_power = new_power;
                }
                
                power_change_count++;
            }
        }
        
        // 2. 安全检查
        EventBits_t event_bits = xEventGroupGetBits(xEventGroupSystem);
        if (event_bits & EVENT_BIT_EMERGENCY_STOP) {
            current_target_power = 0.0f;
            safety_override = true;
        } else {
            safety_override = false;
        }
        
        // 3. 更新PWM输出
        if (abs(current_target_power - last_target_power) > PWM_UPDATE_TOLERANCE || 
            safety_override) {
            
            pwmController.setPower((int)current_target_power);
            last_target_power = current_target_power;
            
            // 调试输出（限制频率）
            static uint32_t last_debug_time = 0;
            if (xTaskGetTickCount() - last_debug_time > pdMS_TO_TICKS(1000)) {
                Serial.printf("[PWM] 功率更新: %.1f%% %s\n", 
                             current_target_power, 
                             safety_override ? "(安全覆盖)" : "");
                last_debug_time = xTaskGetTickCount();
            }
        }
        
        // 4. 更新PWM控制器
        pwmController.update();
        
        // 5. 获取实际PWM状态
        pwm_data.target_power = current_target_power;
        pwm_data.actual_power = (float)pwmController.getCurrentPower();
        pwm_data.pwm_value = pwmController.calculatePWMValue();
        pwm_data.safety_override = safety_override;
        pwm_data.timestamp = xTaskGetTickCount();
        
        // 6. 更新共享状态
        if (xSemaphoreTake(xMutexSharedData, pdMS_TO_TICKS(5)) == pdTRUE) {
            g_system_state.current_pwm_data = pwm_data;
            xSemaphoreGive(xMutexSharedData);
        }
        
        // 7. 性能统计
        static uint32_t pwm_update_count = 0;
        pwm_update_count++;
        if (pwm_update_count % 10000 == 0) { // 每100秒报告一次
            Serial.printf("[PWM] PWM更新: %lu次, 功率变化: %lu次\n", 
                         pwm_update_count, power_change_count);
        }
        
        // 8. 等待下一个周期
        vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(CYCLE_PWM_CONTROL));
    }
}

// ============================================================================
// 实时控制层工具函数
// ============================================================================

bool RealTime_Set_Emergency_Stop(bool enable) {
    if (enable) {
        xEventGroupSetBits(xEventGroupSystem, EVENT_BIT_EMERGENCY_STOP);
        Serial.println("[REALTIME] 🚨 紧急停止激活");
    } else {
        xEventGroupClearBits(xEventGroupSystem, EVENT_BIT_EMERGENCY_STOP);
        Serial.println("[REALTIME] ✅ 紧急停止解除");
    }
    return true;
}

bool RealTime_Check_Safety_Status(void) {
    EventBits_t event_bits = xEventGroupGetBits(xEventGroupSystem);
    return !(event_bits & EVENT_BIT_EMERGENCY_STOP);
}

float RealTime_Get_Current_Power(void) {
    PWMControlData_t pwm_data;
    if (xSemaphoreTake(xMutexSharedData, pdMS_TO_TICKS(10)) == pdTRUE) {
        pwm_data = g_system_state.current_pwm_data;
        xSemaphoreGive(xMutexSharedData);
        return pwm_data.actual_power;
    }
    return 0.0f;
}

void RealTime_Print_Status(void) {
    Serial.println("========== 实时控制层状态 ==========");
    
    // 安全状态
    bool safety_ok = RealTime_Check_Safety_Status();
    Serial.printf("安全状态: %s\n", safety_ok ? "正常" : "紧急停止");
    
    // PWM状态
    PWMControlData_t pwm_data;
    if (xSemaphoreTake(xMutexSharedData, pdMS_TO_TICKS(100)) == pdTRUE) {
        pwm_data = g_system_state.current_pwm_data;
        xSemaphoreGive(xMutexSharedData);
        
        Serial.printf("目标功率: %.1f%%\n", pwm_data.target_power);
        Serial.printf("实际功率: %.1f%%\n", pwm_data.actual_power);
        Serial.printf("PWM值: %d\n", pwm_data.pwm_value);
        Serial.printf("安全覆盖: %s\n", pwm_data.safety_override ? "是" : "否");
    }
    
    // 传感器状态
    SensorData_t sensor_data;
    if (xSemaphoreTake(xMutexSharedData, pdMS_TO_TICKS(100)) == pdTRUE) {
        sensor_data = g_system_state.current_sensor_data;
        xSemaphoreGive(xMutexSharedData);
        
        Serial.printf("豆温: %.1f°C\n", sensor_data.bean_temperature);
        Serial.printf("环温: %.1f°C\n", sensor_data.env_temperature);
        Serial.printf("数据有效: %s\n", sensor_data.valid ? "是" : "否");
    }
    
    Serial.println("==================================");
}

// ============================================================================
// 实时控制层测试函数
// ============================================================================

void RealTime_Test_Safety_System(void) {
    Serial.println("========== 安全系统测试 ==========");
    
    // 测试紧急停止
    Serial.println("1. 测试紧急停止...");
    RealTime_Set_Emergency_Stop(true);
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    float power = RealTime_Get_Current_Power();
    Serial.printf("   紧急停止后功率: %.1f%% (应为0)\n", power);
    
    // 解除紧急停止
    Serial.println("2. 解除紧急停止...");
    RealTime_Set_Emergency_Stop(false);
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    // 测试功率设置
    Serial.println("3. 测试功率设置...");
    System_Set_PWM_Power(25.0f);
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    power = RealTime_Get_Current_Power();
    Serial.printf("   设置25%%后功率: %.1f%%\n", power);
    
    // 恢复到0
    System_Set_PWM_Power(0.0f);
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    Serial.println("✅ 安全系统测试完成");
    Serial.println("===============================");
}


