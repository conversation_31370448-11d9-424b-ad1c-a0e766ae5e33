// freertos_architecture_design.h
// FreeRTOS咖啡烘焙机控制系统架构设计
#ifndef FREERTOS_ARCHITECTURE_DESIGN_H
#define FREERTOS_ARCHITECTURE_DESIGN_H

#include <Arduino.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/queue.h>
#include <freertos/semphr.h>
#include <freertos/timers.h>
#include <freertos/event_groups.h>

// ============================================================================
// 系统架构配置
// ============================================================================

// 任务优先级定义（0-5，数字越大优先级越高）
#define PRIORITY_SAFETY_MONITOR     5    // 安全监控（最高优先级）
#define PRIORITY_PWM_CONTROL        4    // PWM实时控制
#define PRIORITY_PID_CONTROLLER     3    // PID控制算法
#define PRIORITY_PHASE_CONTROL      3    // 相位控制
#define PRIORITY_SENSOR_MANAGER     2    // 传感器管理
#define PRIORITY_FILTER_PROCESS     2    // 滤波处理
#define PRIORITY_SERIAL_COMM        1    // 串口通信
#define PRIORITY_BLE_COMM           1    // BLE通信
#define PRIORITY_DISPLAY_MGR        1    // 显示管理
#define PRIORITY_DATA_RECORD        0    // 数据记录
#define PRIORITY_FILE_MANAGER       0    // 文件管理

// 任务栈大小定义（字节）
#define STACK_SIZE_SAFETY_MONITOR   2048
#define STACK_SIZE_PWM_CONTROL      2048
#define STACK_SIZE_PID_CONTROLLER   3072
#define STACK_SIZE_PHASE_CONTROL    2048
#define STACK_SIZE_SENSOR_MANAGER   2048
#define STACK_SIZE_FILTER_PROCESS   2048
#define STACK_SIZE_SERIAL_COMM      3072
#define STACK_SIZE_BLE_COMM         4096
#define STACK_SIZE_DISPLAY_MGR      2048
#define STACK_SIZE_DATA_RECORD      3072
#define STACK_SIZE_FILE_MANAGER     4096

// 任务执行周期定义（毫秒）
#define CYCLE_SAFETY_MONITOR        20   // 50Hz安全监控
#define CYCLE_PWM_CONTROL           10   // 100Hz PWM控制
#define CYCLE_PID_CONTROLLER        100  // 10Hz PID控制
#define CYCLE_PHASE_CONTROL         50   // 20Hz相位控制
#define CYCLE_SENSOR_MANAGER        200  // 5Hz传感器采集
#define CYCLE_FILTER_PROCESS        100  // 10Hz滤波处理
#define CYCLE_DISPLAY_MGR           1000 // 1Hz显示更新
#define CYCLE_DATA_RECORD           1000 // 1Hz数据记录

// 队列大小定义
#define QUEUE_SIZE_SENSOR_DATA      10   // 传感器数据队列
#define QUEUE_SIZE_CONTROL_CMD      5    // 控制命令队列
#define QUEUE_SIZE_SERIAL_MSG       10   // 串口消息队列
#define QUEUE_SIZE_BLE_MSG          5    // BLE消息队列
#define QUEUE_SIZE_DISPLAY_DATA     3    // 显示数据队列
#define QUEUE_SIZE_LOG_DATA         20   // 日志数据队列

// ============================================================================
// 数据结构定义
// ============================================================================

// 传感器数据结构
typedef struct {
    float bean_temperature;      // 豆温
    float env_temperature;       // 环温
    float pot_value;            // 电位器值
    uint32_t timestamp;         // 时间戳
    bool valid;                 // 数据有效性
} SensorData_t;

// 控制命令结构
typedef struct {
    enum {
        CMD_SET_POWER,          // 设置功率
        CMD_SET_PID_PARAMS,     // 设置PID参数
        CMD_START_ROAST,        // 开始烘焙
        CMD_STOP_ROAST,         // 停止烘焙
        CMD_EMERGENCY_STOP      // 紧急停止
    } command_type;
    
    union {
        struct {
            float power_percent;
        } set_power;
        
        struct {
            float kp, ki, kd;
        } set_pid;
    } params;
    
    uint32_t timestamp;
} ControlCommand_t;

// PWM控制数据结构
typedef struct {
    float target_power;         // 目标功率
    float actual_power;         // 实际功率
    uint16_t pwm_value;         // PWM值
    bool safety_override;       // 安全覆盖
    uint32_t timestamp;
} PWMControlData_t;

// PID控制数据结构
typedef struct {
    float setpoint;             // 设定值
    float input;                // 输入值
    float output;               // 输出值
    float kp, ki, kd;          // PID参数
    bool auto_mode;             // 自动模式
    uint32_t timestamp;
} PIDControlData_t;

// 显示数据结构
typedef struct {
    float bean_temp;            // 豆温
    float env_temp;             // 环温
    float power_percent;        // 功率百分比
    uint32_t roast_time;        // 烘焙时间
    enum {
        STATUS_IDLE,
        STATUS_PREHEATING,
        STATUS_ROASTING,
        STATUS_COOLING,
        STATUS_ERROR
    } status;
    char message[64];           // 状态消息
} DisplayData_t;

// 日志数据结构
typedef struct {
    uint32_t timestamp;         // 时间戳
    float bean_temp;            // 豆温
    float env_temp;             // 环温
    float power_percent;        // 功率
    float pid_output;           // PID输出
    uint16_t ror;              // 升温速率
} LogData_t;

// ============================================================================
// 全局资源定义
// ============================================================================

// 任务句柄
extern TaskHandle_t xTaskSafetyMonitor;
extern TaskHandle_t xTaskPWMControl;
extern TaskHandle_t xTaskPIDController;
extern TaskHandle_t xTaskPhaseControl;
extern TaskHandle_t xTaskSensorManager;
extern TaskHandle_t xTaskFilterProcess;
extern TaskHandle_t xTaskSerialComm;
extern TaskHandle_t xTaskBLEComm;
extern TaskHandle_t xTaskDisplayMgr;
extern TaskHandle_t xTaskDataRecord;
extern TaskHandle_t xTaskFileManager;

// 队列句柄
extern QueueHandle_t xQueueSensorData;
extern QueueHandle_t xQueueControlCmd;
extern QueueHandle_t xQueueSerialMsg;
extern QueueHandle_t xQueueBLEMsg;
extern QueueHandle_t xQueueDisplayData;
extern QueueHandle_t xQueueLogData;

// 信号量句柄
extern SemaphoreHandle_t xMutexSPI;          // SPI总线互斥
extern SemaphoreHandle_t xMutexI2C;          // I2C总线互斥
extern SemaphoreHandle_t xMutexSerial;       // 串口互斥
extern SemaphoreHandle_t xMutexSharedData;   // 共享数据互斥

// 事件组句柄
extern EventGroupHandle_t xEventGroupSystem; // 系统事件组

// 系统事件位定义
#define EVENT_BIT_SYSTEM_READY      (1 << 0)  // 系统就绪
#define EVENT_BIT_SENSORS_READY     (1 << 1)  // 传感器就绪
#define EVENT_BIT_EMERGENCY_STOP    (1 << 2)  // 紧急停止
#define EVENT_BIT_ROAST_ACTIVE      (1 << 3)  // 烘焙激活
#define EVENT_BIT_DATA_LOGGING      (1 << 4)  // 数据记录
#define EVENT_BIT_BLE_CONNECTED     (1 << 5)  // BLE连接
#define EVENT_BIT_SD_CARD_READY     (1 << 6)  // SD卡就绪

// 软件定时器句柄
extern TimerHandle_t xTimerWatchdog;         // 看门狗定时器
extern TimerHandle_t xTimerHeartbeat;        // 心跳定时器

// ============================================================================
// 共享数据结构
// ============================================================================

// 安全状态结构
typedef struct {
    bool emergency_stop;        // 紧急停止状态
    bool temperature_alarm;     // 温度报警
    bool sensor_error;          // 传感器错误
    bool power_overload;        // 功率过载
    uint32_t last_check_time;   // 最后检查时间
} SafetyStatus_t;

// 系统状态结构（需要互斥保护）
typedef struct {
    // 当前状态
    SensorData_t current_sensor_data;
    PWMControlData_t current_pwm_data;
    PIDControlData_t current_pid_data;
    SafetyStatus_t current_safety_status;  // 添加安全状态

    // 系统配置
    bool system_enabled;
    bool safety_mode;
    bool auto_mode;

    // 统计信息
    uint32_t system_uptime;
    uint32_t total_roast_time;
    uint16_t roast_count;

    // 错误状态
    uint32_t error_flags;
    char last_error[64];
} SystemState_t;

extern SystemState_t g_system_state;

// ============================================================================
// 全局变量声明
// ============================================================================

// PID参数
extern float Kp, Ki, Kd;

// 传感器配置
typedef struct {
    float thermocouple_offset;
    float env_temp_offset;
    bool calibration_enabled;
} SensorConfig_t;

extern SensorConfig_t sensor_config;

// 文件系统状态
typedef struct {
    bool sd_card_available;
    bool spiffs_available;
    uint32_t free_space_kb;
    uint32_t total_space_kb;
} FilesystemStatus_t;

extern FilesystemStatus_t fs_status;

// 烘焙会话数据
typedef struct {
    uint32_t session_id;
    uint32_t start_time;
    uint32_t end_time;
    float max_bean_temp;
    float total_energy;
    uint16_t data_points;
    bool active;
} RoastSession_t;

extern RoastSession_t current_roast_session;

// ============================================================================
// 核心API声明
// ============================================================================

// 系统初始化
bool FreeRTOS_System_Init(void);
bool FreeRTOS_Create_Tasks(void);
bool FreeRTOS_Create_Queues(void);
bool FreeRTOS_Create_Semaphores(void);
bool FreeRTOS_Create_EventGroups(void);
bool FreeRTOS_Create_Timers(void);

// 任务函数声明
void Task_Safety_Monitor(void *pvParameters);
void Task_PWM_Control(void *pvParameters);
void Task_PID_Controller(void *pvParameters);
void Task_Phase_Control(void *pvParameters);
void Task_Sensor_Manager(void *pvParameters);
void Task_Filter_Process(void *pvParameters);
void Task_Serial_Comm(void *pvParameters);
void Task_BLE_Comm(void *pvParameters);
void Task_Display_Mgr(void *pvParameters);
void Task_Data_Record(void *pvParameters);
void Task_File_Manager(void *pvParameters);

// 定时器回调函数
void Timer_Watchdog_Callback(TimerHandle_t xTimer);
void Timer_Heartbeat_Callback(TimerHandle_t xTimer);

// 工具函数
bool System_Send_Control_Command(ControlCommand_t *cmd);
bool System_Get_Sensor_Data(SensorData_t *data, TickType_t timeout);
bool System_Set_PWM_Power(float power_percent);
bool System_Emergency_Stop(void);
void System_Print_Task_Stats(void);
void System_Print_Memory_Stats(void);

// 按键处理函数 - FreeRTOS版本
void check_button_freertos(void);
void handle_short_press_freertos(void);
void handle_long_press_freertos(void);

// 电位器读取函数
float read_potentiometer(void);

// ============================================================================
// 传感器相关函数声明
// ============================================================================
bool init_sensors(void);
float read_thermocouple(void);
float read_env_temperature(void);
void apply_sensor_calibration(SensorData_t* data);
bool detect_sensor_anomalies(SensorData_t* data);

// ============================================================================
// 实时控制相关函数声明
// ============================================================================
float RealTime_Get_Current_Power(void);
bool RealTime_Check_Safety_Status(void);
void RealTime_Print_Status(void);
bool RealTime_Set_Emergency_Stop(bool enable);
void RealTime_Test_Safety_System(void);

// ============================================================================
// 控制算法相关函数声明
// ============================================================================
void execute_pid_control(const SensorData_t& sensor_data, PIDControlData_t* pid_data);
void execute_curve_following(const SensorData_t& sensor_data, PIDControlData_t* pid_data);
void execute_adaptive_control(const SensorData_t& sensor_data, PIDControlData_t* pid_data);
bool Control_Set_PID_Parameters(float kp, float ki, float kd);
bool Control_Start_Roast(float target_temp);
bool Control_Stop_Roast(void);
void Control_Print_Status(void);
void Control_Test_PID_Response(void);

// ============================================================================
// 数据处理相关函数声明
// ============================================================================
float calculate_ror(float current_temp);
void DataProcessing_Print_Status(void);

// ============================================================================
// 通信相关函数声明
// ============================================================================
void process_serial_command(const char* command);
void send_system_status_serial(void);
void parse_pid_command(const String& cmd);
void print_serial_help(void);
void send_welcome_message_ble(void);
void send_data_notification_ble(void);

// ============================================================================
// 显示相关函数声明
// ============================================================================
bool init_display(void);
void display_main_page(const DisplayData_t* data);
void display_system_page(void);
void display_pid_page(void);
void display_network_page(void);
void update_status_indicators(void);

// ============================================================================
// 文件系统相关函数声明
// ============================================================================
bool init_data_recording(void);
bool write_log_buffer_to_file(const LogData_t* buffer, uint16_t count);
void update_roast_session(const LogData_t* data);
void perform_file_cleanup(void);
void update_filesystem_status(void);
uint32_t generate_session_id(void);
void save_roast_session_summary(void);
void perform_file_maintenance(void);
void check_config_file_changes(void);

#endif // FREERTOS_ARCHITECTURE_DESIGN_H
