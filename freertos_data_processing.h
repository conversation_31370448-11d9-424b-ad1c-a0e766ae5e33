#ifndef FREERTOS_DATA_PROCESSING_H
#define FREERTOS_DATA_PROCESSING_H

#include "freertos_architecture_design.h"

// ============================================================================
// FreeRTOS数据处理头文件
// ============================================================================

// 数据处理任务
void Task_Sensor_Manager(void *pvParameters);
void Task_Data_Logger(void *pvParameters);
void Task_Data_Record(void *pvParameters);

// 传感器配置
typedef struct {
    bool temp_enabled;
    bool pot_enabled;
    bool pressure_enabled;
    uint16_t sample_rate_ms;
    uint8_t filter_samples;
} SensorConfig_t;

// 传感器数据处理
bool Sensor_Init_All(void);
bool Sensor_Read_Temperature(float *temp);
bool Sensor_Read_Environment(float *env_temp);
float Sensor_Apply_Filter(float new_value, float *history, uint8_t samples);

// 电位器和按键
float read_potentiometer(void);
void check_button_freertos(void);
void handle_short_press_freertos(void);
void handle_long_press_freertos(void);

// 数据记录
bool Data_Start_Recording(void);
bool Data_Stop_Recording(void);
bool Data_Save_Point(SensorData_t *data);

// 数据验证
bool Data_Validate_Sensor_Reading(float value, float min, float max);
void Data_Apply_Calibration(SensorData_t *data);

#endif // FREERTOS_DATA_PROCESSING_H
