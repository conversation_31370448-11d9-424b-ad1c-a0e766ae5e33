// pll_protection_system.cpp
// 数字锁相环多重保护系统实现
#include "pll_protection_system.h"

// ============================================================================
// PhaseLossDetector 类实现
// ============================================================================

PhaseLossDetector::PhaseLossDetector() {
    last_good_phase = millis();
}

void PhaseLossDetector::update(float phase_error, bool pll_locked) {
    unsigned long current_time = millis();
    
    // 检查相位误差
    if (abs(phase_error) > phase_error_threshold || !pll_locked) {
        consecutive_errors++;
        
        if (consecutive_errors >= consecutive_error_limit) {
            if (!phase_loss_detected) {
                phase_loss_detected = true;
                total_detections++;
                Serial.printf("[PHASE_DETECTOR] 相位失联检测！连续错误:%d 相位误差:%.4f\n", 
                             consecutive_errors, phase_error);
            }
        }
    } else {
        // 相位正常
        if (phase_loss_detected) {
            phase_loss_detected = false;
            Serial.println("[PHASE_DETECTOR] 相位连接恢复");
        }
        consecutive_errors = 0;
        last_good_phase = current_time;
    }
    
    // 检查长时间无良好相位
    if (current_time - last_good_phase > detection_window) {
        if (!phase_loss_detected) {
            phase_loss_detected = true;
            total_detections++;
            Serial.printf("[PHASE_DETECTOR] 长时间相位失联！持续时间:%lu ms\n", 
                         current_time - last_good_phase);
        }
    }
}

bool PhaseLossDetector::isPhaseLossDetected() {
    return phase_loss_detected;
}

void PhaseLossDetector::reset() {
    consecutive_errors = 0;
    phase_loss_detected = false;
    last_good_phase = millis();
    Serial.println("[PHASE_DETECTOR] 相位检测器已重置");
}

void PhaseLossDetector::setThreshold(float threshold) {
    phase_error_threshold = constrain(threshold, 0.01f, 1.0f);
    Serial.printf("[PHASE_DETECTOR] 相位误差阈值设置为: %.4f\n", phase_error_threshold);
}

float PhaseLossDetector::getDetectionAccuracy() {
    if (total_detections == 0) return 100.0f;
    return ((float)(total_detections - false_alarms) / total_detections) * 100.0f;
}

void PhaseLossDetector::printStatus() {
    Serial.println("========== 相位失联检测器状态 ==========");
    Serial.printf("相位失联: %s\n", phase_loss_detected ? "是" : "否");
    Serial.printf("连续错误: %d/%d\n", consecutive_errors, consecutive_error_limit);
    Serial.printf("相位误差阈值: %.4f\n", phase_error_threshold);
    Serial.printf("总检测次数: %d\n", total_detections);
    Serial.printf("误报次数: %d\n", false_alarms);
    Serial.printf("检测精度: %.2f%%\n", getDetectionAccuracy());
    
    unsigned long time_since_good = millis() - last_good_phase;
    Serial.printf("上次良好相位: %lu ms前\n", time_since_good);
    Serial.println("========================================");
}

// ============================================================================
// PowerDriftMonitor 类实现
// ============================================================================

PowerDriftMonitor::PowerDriftMonitor() {
    for (int i = 0; i < 50; i++) {
        power_history[i] = 0.0f;
    }
}

void PowerDriftMonitor::update(float target_power, float actual_power) {
    // 记录功率历史
    power_history[history_index] = actual_power;
    history_index = (history_index + 1) % 50;
    
    // 计算漂移速率
    current_drift_rate = calculateDriftRate();
    
    // 检测漂移
    float power_error = abs(actual_power - target_power);
    
    if (power_error > drift_threshold) {
        if (!drift_detected) {
            drift_detected = true;
            drift_events++;
            Serial.printf("[DRIFT_MONITOR] 功率漂移检测！目标:%.1f%% 实际:%.1f%% 误差:%.1f%%\n",
                         target_power, actual_power, power_error);
        }
        
        // 检测快速漂移
        if (power_error > rapid_drift_threshold) {
            if (!rapid_drift_detected) {
                rapid_drift_detected = true;
                Serial.printf("[DRIFT_MONITOR] 快速功率漂移警告！误差:%.1f%%\n", power_error);
            }
        }
    } else {
        if (drift_detected) {
            drift_detected = false;
            rapid_drift_detected = false;
            Serial.println("[DRIFT_MONITOR] 功率漂移恢复正常");
        }
    }
    
    // 更新统计
    updateStatistics();
}

float PowerDriftMonitor::calculateDriftRate() {
    if (history_index < 10) return 0.0f;
    
    // 计算最近10个点的变化率
    int start_idx = (history_index - 10 + 50) % 50;
    int end_idx = (history_index - 1 + 50) % 50;
    
    float start_value = power_history[start_idx];
    float end_value = power_history[end_idx];
    
    return (end_value - start_value) / 10.0f;
}

void PowerDriftMonitor::updateStatistics() {
    float current_error = abs(current_drift_rate);
    if (current_error > max_drift_detected) {
        max_drift_detected = current_error;
    }
}

bool PowerDriftMonitor::isDriftDetected() {
    return drift_detected;
}

bool PowerDriftMonitor::isRapidDriftDetected() {
    return rapid_drift_detected;
}

float PowerDriftMonitor::getDriftRate() {
    return current_drift_rate;
}

void PowerDriftMonitor::reset() {
    for (int i = 0; i < 50; i++) {
        power_history[i] = 0.0f;
    }
    history_index = 0;
    current_drift_rate = 0.0f;
    drift_detected = false;
    rapid_drift_detected = false;
    Serial.println("[DRIFT_MONITOR] 功率漂移监测器已重置");
}

void PowerDriftMonitor::setThresholds(float normal, float rapid) {
    drift_threshold = constrain(normal, 1.0f, 20.0f);
    rapid_drift_threshold = constrain(rapid, 5.0f, 50.0f);
    Serial.printf("[DRIFT_MONITOR] 漂移阈值设置 - 正常:%.1f%% 快速:%.1f%%\n", 
                 drift_threshold, rapid_drift_threshold);
}

void PowerDriftMonitor::printStatus() {
    Serial.println("========== 功率漂移监测器状态 ==========");
    Serial.printf("功率漂移: %s\n", drift_detected ? "是" : "否");
    Serial.printf("快速漂移: %s\n", rapid_drift_detected ? "是" : "否");
    Serial.printf("当前漂移速率: %.3f%%/采样\n", current_drift_rate);
    Serial.printf("漂移阈值: %.1f%%\n", drift_threshold);
    Serial.printf("快速漂移阈值: %.1f%%\n", rapid_drift_threshold);
    Serial.printf("漂移事件数: %d\n", drift_events);
    Serial.printf("最大检测漂移: %.2f%%\n", max_drift_detected);
    Serial.println("========================================");
}

// ============================================================================
// FrequencyDeviationMonitor 类实现
// ============================================================================

FrequencyDeviationMonitor::FrequencyDeviationMonitor() {
    current_frequency = nominal_frequency;
}

void FrequencyDeviationMonitor::update(float vco_frequency) {
    current_frequency = vco_frequency;
    frequency_deviation = abs(vco_frequency - nominal_frequency);
    
    if (frequency_deviation > deviation_threshold) {
        if (!deviation_detected) {
            deviation_detected = true;
            deviation_events++;
            Serial.printf("[FREQ_MONITOR] 频率偏差检测！当前:%.3fHz 标称:%.3fHz 偏差:%.3fHz\n",
                         vco_frequency, nominal_frequency, frequency_deviation);
        }
        
        // 检查临界偏差
        if (frequency_deviation > critical_deviation) {
            Serial.printf("[FREQ_MONITOR] 临界频率偏差警告！偏差:%.3fHz\n", frequency_deviation);
        }
    } else {
        if (deviation_detected) {
            deviation_detected = false;
            Serial.println("[FREQ_MONITOR] 频率偏差恢复正常");
        }
    }
    
    // 更新最大偏差
    if (frequency_deviation > max_deviation) {
        max_deviation = frequency_deviation;
    }
}

bool FrequencyDeviationMonitor::isDeviationDetected() {
    return deviation_detected;
}

float FrequencyDeviationMonitor::getDeviation() {
    return frequency_deviation;
}

void FrequencyDeviationMonitor::reset() {
    current_frequency = nominal_frequency;
    frequency_deviation = 0.0f;
    deviation_detected = false;
    max_deviation = 0.0f;
    Serial.println("[FREQ_MONITOR] 频率偏差监测器已重置");
}

void FrequencyDeviationMonitor::setThresholds(float normal, float critical) {
    deviation_threshold = constrain(normal, 0.1f, 2.0f);
    critical_deviation = constrain(critical, 0.5f, 5.0f);
    Serial.printf("[FREQ_MONITOR] 频率偏差阈值设置 - 正常:%.3fHz 临界:%.3fHz\n", 
                 deviation_threshold, critical_deviation);
}

void FrequencyDeviationMonitor::printStatus() {
    Serial.println("========== 频率偏差监测器状态 ==========");
    Serial.printf("频率偏差: %s\n", deviation_detected ? "是" : "否");
    Serial.printf("当前频率: %.3f Hz\n", current_frequency);
    Serial.printf("标称频率: %.3f Hz\n", nominal_frequency);
    Serial.printf("当前偏差: %.3f Hz\n", frequency_deviation);
    Serial.printf("偏差阈值: %.3f Hz\n", deviation_threshold);
    Serial.printf("临界阈值: %.3f Hz\n", critical_deviation);
    Serial.printf("偏差事件数: %d\n", deviation_events);
    Serial.printf("最大偏差: %.3f Hz\n", max_deviation);
    Serial.println("========================================");
}

// ============================================================================
// SystemOverloadDetector 类实现
// ============================================================================

SystemOverloadDetector::SystemOverloadDetector() {
    last_reset_time = millis();
}

void SystemOverloadDetector::recordUpdate() {
    updates_count++;
    checkOverload();
    updateStatistics();
}

void SystemOverloadDetector::recordCorrection() {
    corrections_count++;
    checkOverload();
}

void SystemOverloadDetector::checkOverload() {
    unsigned long current_time = millis();
    
    // 每秒重置计数器
    if (current_time - last_reset_time >= 1000) {
        // 检查更新频率
        if (updates_count > max_updates_per_second) {
            overload_detected = true;
            Serial.printf("[OVERLOAD_DETECTOR] 更新频率过载！%d次/秒 (限制:%d)\n", 
                         updates_count, max_updates_per_second);
        }
        
        // 检查校正频率（每分钟）
        static int minute_corrections = 0;
        static unsigned long last_minute = 0;
        
        minute_corrections += corrections_count;
        if (current_time - last_minute >= 60000) {
            if (minute_corrections > max_corrections_per_minute) {
                overload_detected = true;
                Serial.printf("[OVERLOAD_DETECTOR] 校正频率过载！%d次/分钟 (限制:%d)\n", 
                             minute_corrections, max_corrections_per_minute);
            }
            minute_corrections = 0;
            last_minute = current_time;
        }
        
        // 重置计数器
        updates_count = 0;
        corrections_count = 0;
        last_reset_time = current_time;
        
        // 如果没有过载，清除过载标志
        if (overload_detected) {
            overload_detected = false;
        }
    }
}

void SystemOverloadDetector::updateStatistics() {
    static unsigned long last_update_time = 0;
    unsigned long current_time = millis();
    
    if (last_update_time > 0) {
        float interval = current_time - last_update_time;
        avg_update_interval = (avg_update_interval * 0.9f) + (interval * 0.1f);
        
        if (interval > max_update_interval) {
            max_update_interval = interval;
        }
    }
    
    last_update_time = current_time;
}

bool SystemOverloadDetector::isOverloadDetected() {
    return overload_detected;
}

void SystemOverloadDetector::reset() {
    updates_count = 0;
    corrections_count = 0;
    overload_detected = false;
    avg_update_interval = 0.0f;
    max_update_interval = 0.0f;
    last_reset_time = millis();
    Serial.println("[OVERLOAD_DETECTOR] 系统过载检测器已重置");
}

void SystemOverloadDetector::setLimits(int max_updates, int max_corrections) {
    max_updates_per_second = constrain(max_updates, 100, 10000);
    max_corrections_per_minute = constrain(max_corrections, 10, 1000);
    Serial.printf("[OVERLOAD_DETECTOR] 过载限制设置 - 更新:%d/秒 校正:%d/分钟\n", 
                 max_updates_per_second, max_corrections_per_minute);
}

void SystemOverloadDetector::printStatus() {
    Serial.println("========== 系统过载检测器状态 ==========");
    Serial.printf("系统过载: %s\n", overload_detected ? "是" : "否");
    Serial.printf("当前更新数: %d/%d (每秒)\n", updates_count, max_updates_per_second);
    Serial.printf("当前校正数: %d/%d (每分钟)\n", corrections_count, max_corrections_per_minute);
    Serial.printf("平均更新间隔: %.2f ms\n", avg_update_interval);
    Serial.printf("最大更新间隔: %.2f ms\n", max_update_interval);
    Serial.println("========================================");
}

// ============================================================================
// ProtectionController 类实现
// ============================================================================

ProtectionController::ProtectionController() {
    phase_detector = nullptr;
    drift_monitor = nullptr;
    freq_monitor = nullptr;
    overload_detector = nullptr;

    last_protection_check = millis();
}

ProtectionController::~ProtectionController() {
    if (phase_detector) delete phase_detector;
    if (drift_monitor) delete drift_monitor;
    if (freq_monitor) delete freq_monitor;
    if (overload_detector) delete overload_detector;
}

bool ProtectionController::begin() {
    Serial.println("[PROTECTION] 初始化多重保护系统...");

    // 创建保护组件
    phase_detector = new PhaseLossDetector();
    if (!phase_detector) {
        Serial.println("[PROTECTION] 错误：无法创建相位检测器");
        return false;
    }

    drift_monitor = new PowerDriftMonitor();
    if (!drift_monitor) {
        Serial.println("[PROTECTION] 错误：无法创建漂移监测器");
        return false;
    }

    freq_monitor = new FrequencyDeviationMonitor();
    if (!freq_monitor) {
        Serial.println("[PROTECTION] 错误：无法创建频率监测器");
        return false;
    }

    overload_detector = new SystemOverloadDetector();
    if (!overload_detector) {
        Serial.println("[PROTECTION] 错误：无法创建过载检测器");
        return false;
    }

    Serial.println("[PROTECTION] 多重保护系统初始化完成");
    Serial.printf("[PROTECTION] 保护级别: %d\n", protection_level);

    return true;
}

void ProtectionController::update(DigitalPLLControl* pll, PowerFeedbackMonitor* monitor) {
    unsigned long current_time = millis();

    // 检查更新间隔
    if (current_time - last_protection_check < protection_check_interval) {
        return;
    }

    last_protection_check = current_time;

    // 记录系统活动
    if (overload_detector) {
        overload_detector->recordUpdate();
    }

    // 检查保护条件
    checkProtectionConditions(pll, monitor);

    // 评估保护级别
    evaluateProtectionLevel();

    // 执行保护动作
    executeProtectionActions();

    // 更新统计
    updateProtectionStatistics();
}

void ProtectionController::checkProtectionConditions(DigitalPLLControl* pll, PowerFeedbackMonitor* monitor) {
    if (!pll || !monitor) return;

    // 更新各检测器
    if (phase_detector) {
        phase_detector->update(pll->getPhaseError(), pll->isPLLLocked());
    }

    if (drift_monitor) {
        int current_power = pll->getCurrentPower();
        drift_monitor->update((float)current_power, (float)current_power);
    }

    if (freq_monitor) {
        freq_monitor->update(pll->getVCOFrequency());
    }

    // 检查故障类型
    if (phase_detector && phase_detector->isPhaseLossDetected()) {
        if (active_fault == FAULT_NONE) {
            active_fault = FAULT_PHASE_LOSS;
            handlePhaseLossFault();
        }
    }
    else if (drift_monitor && drift_monitor->isRapidDriftDetected()) {
        if (active_fault == FAULT_NONE || active_fault == FAULT_POWER_DRIFT) {
            active_fault = FAULT_POWER_DRIFT;
            handlePowerDriftFault();
        }
    }
    else if (freq_monitor && freq_monitor->isDeviationDetected()) {
        if (active_fault == FAULT_NONE || active_fault == FAULT_FREQUENCY_DEVIATION) {
            active_fault = FAULT_FREQUENCY_DEVIATION;
            handleFrequencyDeviationFault();
        }
    }
    else if (overload_detector && overload_detector->isOverloadDetected()) {
        if (active_fault == FAULT_NONE || active_fault == FAULT_SYSTEM_OVERLOAD) {
            active_fault = FAULT_SYSTEM_OVERLOAD;
            handleSystemOverloadFault();
        }
    }
    else {
        // 无故障
        if (active_fault != FAULT_NONE) {
            Serial.printf("[PROTECTION] 故障已清除: %d\n", active_fault);
            active_fault = FAULT_NONE;
            current_status = PROTECTION_NORMAL;
        }
    }
}

void ProtectionController::evaluateProtectionLevel() {
    // 根据故障严重程度调整保护状态
    switch (active_fault) {
        case FAULT_NONE:
            current_status = PROTECTION_NORMAL;
            break;

        case FAULT_POWER_DRIFT:
            current_status = (drift_monitor && drift_monitor->isRapidDriftDetected()) ?
                           PROTECTION_ALERT : PROTECTION_WARNING;
            break;

        case FAULT_PHASE_LOSS:
            current_status = PROTECTION_ALERT;
            break;

        case FAULT_FREQUENCY_DEVIATION:
            current_status = PROTECTION_WARNING;
            break;

        case FAULT_SYSTEM_OVERLOAD:
            current_status = PROTECTION_EMERGENCY;
            break;

        case FAULT_HARDWARE_ERROR:
        case FAULT_COMMUNICATION_LOSS:
            current_status = PROTECTION_EMERGENCY;
            break;

        default:
            break;
    }
}

void ProtectionController::executeProtectionActions() {
    static ProtectionStatus last_status = PROTECTION_NORMAL;

    if (current_status != last_status) {
        protection_activations++;

        switch (current_status) {
            case PROTECTION_WARNING:
                Serial.println("[PROTECTION] 保护警告激活");
                break;

            case PROTECTION_ALERT:
                Serial.println("[PROTECTION] 保护报警激活");
                if (auto_recovery_enabled) {
                    attemptRecovery();
                }
                break;

            case PROTECTION_EMERGENCY:
                Serial.println("[PROTECTION] 紧急保护激活");
                if (emergency_shutdown_enabled) {
                    triggerEmergencyShutdown();
                }
                break;

            case PROTECTION_SHUTDOWN:
                Serial.println("[PROTECTION] 系统已关闭");
                break;

            default:
                break;
        }

        last_status = current_status;
    }
}
