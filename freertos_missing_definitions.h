// freertos_missing_definitions.h
// 缺失定义的补充头文件
#ifndef FREERTOS_MISSING_DEFINITIONS_H
#define FREERTOS_MISSING_DEFINITIONS_H

#include <Arduino.h>
#include <PID_v1.h>

// ============================================================================
// PID控制器全局变量
// ============================================================================
extern double Input, Output, Setpoint;
extern PID myPID;

// ============================================================================
// BLE相关定义
// ============================================================================
#ifdef USE_BLE
#include <BLEDevice.h>
#include <BLEServer.h>
#include <BLEUtils.h>
#include <BLE2902.h>

extern BLEServer* pServer;
extern BLECharacteristic* pCharacteristic;
extern bool deviceConnected;
#endif

// ============================================================================
// 显示页面枚举
// ============================================================================
typedef enum {
    DISPLAY_PAGE_MAIN = 0,
    DISPLAY_PAGE_SYSTEM,
    DISPLAY_PAGE_PID,
    DISPLAY_PAGE_NETWORK
} DisplayPage_t;

extern DisplayPage_t current_display_page;

// ============================================================================
// 缺失的常量定义
// ============================================================================
#define STATUS_IDLE         0
#define STATUS_PREHEATING   1
#define STATUS_ROASTING     2
#define STATUS_COOLING      3
#define STATUS_ERROR        4

// ============================================================================
// 传感器相关常量
// ============================================================================
#define TEMP_UPDATE_INTERVAL    200  // 温度更新间隔(ms)
#define MAX_TEMP_CHANGE         50.0f // 最大温度变化阈值
#define MIN_VALID_TEMP          -10.0f
#define MAX_VALID_TEMP          300.0f

// ============================================================================
// 文件系统相关常量
// ============================================================================
#define MAX_LOG_BUFFER_SIZE     100
#define MAX_FILENAME_LENGTH     64
#define CONFIG_FILE_PATH        "/config.json"
#define ROAST_DATA_DIR          "/roast_data"

// ============================================================================
// 网络相关常量
// ============================================================================
#define BLE_DEVICE_NAME         "CoffeeRoaster"
#define BLE_SERVICE_UUID        "12345678-1234-1234-1234-123456789abc"
#define BLE_CHARACTERISTIC_UUID "*************-4321-4321-cba987654321"

// ============================================================================
// 安全相关常量
// ============================================================================
#define MAX_SAFE_TEMP           250.0f
#define MAX_SAFE_POWER          85.0f
#define EMERGENCY_COOLDOWN_TIME 30000  // 30秒

// ============================================================================
// 滤波器相关常量
// ============================================================================
#define FILTER_WINDOW_SIZE      10
#define KALMAN_Q                0.08f
#define KALMAN_R                2.0f

// ============================================================================
// 通信相关常量
// ============================================================================
#define SERIAL_BUFFER_SIZE      256
#define BLE_BUFFER_SIZE         512
#define COMMAND_TIMEOUT_MS      5000

// ============================================================================
// 任务相关常量
// ============================================================================
#define TASK_NOTIFICATION_TIMEOUT   1000
#define QUEUE_SEND_TIMEOUT          100
#define MUTEX_TAKE_TIMEOUT          100

// ============================================================================
// 错误代码定义
// ============================================================================
#define ERROR_NONE              0x00000000
#define ERROR_SENSOR_FAIL       0x00000001
#define ERROR_TEMP_HIGH         0x00000002
#define ERROR_COMM_FAIL         0x00000004
#define ERROR_FILE_FAIL         0x00000008
#define ERROR_MEMORY_FAIL       0x00000010

// ============================================================================
// 调试相关宏
// ============================================================================
#ifdef DEBUG_FREERTOS
#define DEBUG_PRINT(x)          Serial.print(x)
#define DEBUG_PRINTLN(x)        Serial.println(x)
#define DEBUG_PRINTF(...)       Serial.printf(__VA_ARGS__)
#else
#define DEBUG_PRINT(x)
#define DEBUG_PRINTLN(x)
#define DEBUG_PRINTF(...)
#endif

// ============================================================================
// 实用宏定义
// ============================================================================
#define ARRAY_SIZE(arr)         (sizeof(arr) / sizeof((arr)[0]))
#define MIN(a, b)               ((a) < (b) ? (a) : (b))
#define MAX(a, b)               ((a) > (b) ? (a) : (b))
#define CLAMP(x, min, max)      (MIN(MAX(x, min), max))

// ============================================================================
// 版本信息
// ============================================================================
#define FREERTOS_VERSION_MAJOR  1
#define FREERTOS_VERSION_MINOR  0
#define FREERTOS_VERSION_PATCH  0
#define FREERTOS_BUILD_DATE     __DATE__
#define FREERTOS_BUILD_TIME     __TIME__

#endif // FREERTOS_MISSING_DEFINITIONS_H
