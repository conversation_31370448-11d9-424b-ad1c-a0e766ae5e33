#ifndef FREERTOS_CONTROL_ALGORITHMS_H
#define FREERTOS_CONTROL_ALGORITHMS_H

#include "freertos_architecture_design.h"

// ============================================================================
// FreeRTOS控制算法头文件
// ============================================================================

// 控制算法任务
void Task_Control_Algorithms(void *pvParameters);

// PID控制器
typedef struct {
    float kp, ki, kd;
    float setpoint;
    float integral;
    float previous_error;
    float output_min, output_max;
} PIDController_t;

// PID控制函数
void PID_Init(PIDController_t *pid, float kp, float ki, float kd);
float PID_Update(PIDController_t *pid, float current_value, float dt);
void PID_Reset(PIDController_t *pid);

// 控制算法函数
bool Control_Start_Roast(float target_temp);
bool Control_Stop_Roast(void);
bool Control_Set_Manual_Power(float power_percent);
bool Control_Set_Auto_Mode(bool enable);

// 温度控制
float Control_Temperature_PID(float current_temp, float target_temp);
void Control_Apply_Temperature_Curve(void);

// 功率管理
void Control_Power_Management(void);
void Control_Safety_Monitoring(void);

#endif // FREERTOS_CONTROL_ALGORITHMS_H
