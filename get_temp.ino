#include "get_temp.h"
#include <vector>
#include <numeric> // Required for std::accumulate if needed later, good practice to include
     // 导入MAX6675库

// 简洁架构：只使用卡尔曼滤波保证真实性
static KalmanFilter rorKalmanFilter(0.08f, 2.0f);  // 与温度滤波一致的参数

// BT和ET卡尔曼滤波器
#ifdef USE_KALMAN_FILTER
static KalmanFilter btKalmanFilter(Q_kalman, R_kalman);
static KalmanFilter etKalmanFilter(ET_Q_kalman, ET_R_kalman);
#endif

#include <math.h> // For abs() and isnan()

// 存储BT历史数据用于ROR计算
static std::vector<float> bt_history; // BT历史数据
// static float initial_BT = NAN; // 不再需要初始BT，改用历史数据计算
const int ROR_WINDOW_SECONDS = 60;  // 60秒窗口保持不变
const int SAMPLES_IN_ROR_WINDOW = 60;  // 1秒采样率时，60秒窗口=60个样本

// 移除滤波器实例，直接使用原始读数
void get_samples()
{

  static uint32_t startTime = 0; // 记录启动时间
  if (startTime == 0) {
    startTime = millis(); // 首次调用时记录启动时间
  }
  static uint32_t therm_count=0;
  static uint32_t last_therm_count=0;
  static float last_BT = 0;
  static float last_valid_BT = NAN; // Store last valid BT
  static float last_valid_ET = NAN; // Store last valid ET
  // 创建6675对象
  static MAX6675 bt_sensor(thermoCLK, thermoCS1, thermoDO);  // BT传感器
  static MAX6675 et_sensor(thermoCLK, thermoCS2, thermoDO);  // ET传感器


  if (millis() - therm_count > TEMP_UPDATE_INTERVAL)
  {
    therm_count = millis();
    float current_BT_reading, current_ET_reading,current_RT_reading; // Temporary variables for new readings
    
    if (useCelsiusScale)
    { // 获取摄氏度
      // 直接读取原始值
      current_BT_reading = bt_sensor.readCelsius();
      current_ET_reading = et_sensor.readCelsius();
      current_RT_reading = mlx.readObjectTempC();

      // "终极丝滑"方案：使用卡尔曼滤波器进行平滑处理
      if (!isnan(current_BT_reading)) {
        beanTemperature = btKalmanFilter.update(current_BT_reading);
      }
      
      if (!USE_INFRARED_TEMPERATURE) {
        if (!isnan(current_ET_reading)) {
          exhaustTemperature = etKalmanFilter.update(current_ET_reading);
        }
      } else {
        exhaustTemperature = current_RT_reading; // 红外温度不使用卡尔曼滤波
      }
    
      // 移除温度变化率限制器，只使用卡尔曼滤波保证真实性
      // 原始数据 → 卡尔曼滤波 → 最终输出 的简洁架构
    

    
      // 简单异常检测 (Apply to filtered ET)
      if (exhaustTemperature > 300 || exhaustTemperature < 0) {
        // 如果ET读数异常，仅记录警告，因为滤波应该处理突变
        // delay(10);
        // exhaustTemperature = et_sensor.readCelsius(); // Re-reading bypasses filter
        Serial.println("[WARN] Filtered ET reading is outside expected range (0-300 C)");
      }
      
      //Serial.printf("[TEMP_DEBUG] C BT: %.2f ET: %.2f\n", beanTemperature, exhaustTemperature);
    }
    else
    { // 获取华氏度
      // 直接读取原始值
      current_BT_reading = bt_sensor.readFahrenheit();
      current_ET_reading = et_sensor.readFahrenheit();

      // Filter BT (Threshold: 30C * 1.8 = 54F)
      if (isnan(last_valid_BT) || abs(current_BT_reading - last_valid_BT) <= 54.0) { 
          last_valid_BT = current_BT_reading;
      }
      beanTemperature = last_valid_BT;

      // Filter ET (Threshold: 30C * 1.8 = 54F)
      if (isnan(last_valid_ET) || abs(current_ET_reading - last_valid_ET) <= 54.0) { 
          last_valid_ET = current_ET_reading;
      }
      exhaustTemperature = last_valid_ET;
      
      // 移除华氏度版本的温度变化率限制器，只使用卡尔曼滤波保证真实性
      // 原始数据 → 卡尔曼滤波 → 最终输出 的简洁架构
      

      
      // 简单异常检测 (Apply to filtered ET)
      if (exhaustTemperature > 572 || exhaustTemperature < 32) { // 华氏度对应的异常范围
        // 如果ET读数异常，仅记录警告
        // delay(10);
        // exhaustTemperature = et_sensor.readFahrenheit(); // Re-reading bypasses filter
        Serial.println("[WARN] Filtered ET reading is outside expected range (32-572 F)");
      }
      
      //Serial.printf("[TEMP_DEBUG] F BT: %.2f ET: %.2f\n", beanTemperature, exhaustTemperature);
    }
    
    // 简化ROR计算：始终计算，只使用卡尔曼滤波保证真实性
    float current_BT = beanTemperature;
    
    // 将当前有效BT值添加到历史记录
    if (!isnan(current_BT)) {
        bt_history.push_back(current_BT);
        // 限制历史记录大小，防止内存溢出
        const size_t MAX_HISTORY_SIZE = 3600; // 最大保存1小时的数据
        if (bt_history.size() > MAX_HISTORY_SIZE) { 
            bt_history.erase(bt_history.begin()); 
        }
    }

    float calculated_ror = 0;
    size_t history_size = bt_history.size();

    // 始终计算ROR，只保留卡尔曼滤波
    if (history_size > 1) { 
        if (history_size <= SAMPLES_IN_ROR_WINDOW) {
            // 使用当前窗口内的所有数据计算ROR
            float first_bt_in_window = bt_history.front();
            float duration_seconds = (history_size - 1) * (TEMP_UPDATE_INTERVAL / 1000.0f);
            if (duration_seconds > 1e-6) {
                calculated_ror = (current_BT - first_bt_in_window) * 60.0f / duration_seconds;
            }
        } else {
            // 使用标准的60秒窗口计算ROR
            float bt_60s_ago = bt_history[history_size - 1 - SAMPLES_IN_ROR_WINDOW];
            calculated_ror = current_BT - bt_60s_ago;
        }
    } else {
        calculated_ror = 0; // 数据不足
    }

    // 只应用卡尔曼滤波，保证真实性
    float filtered_ror = rorKalmanFilter.update(calculated_ror);
    rateOfRise = filtered_ror;  // 移除约束，保持真实值
    
    last_BT = current_BT;
    // 更新上一次的 BT时间戳
    last_therm_count = therm_count;
  }
}