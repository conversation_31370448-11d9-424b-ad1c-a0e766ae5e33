// digital_pll_config.h
// 数字锁相环系统配置和测试
#ifndef DIGITAL_PLL_CONFIG_H
#define DIGITAL_PLL_CONFIG_H

#include <Arduino.h>
#include "digital_pll_integration.h"
#include "pll_protection_system.h"

// 系统配置开关
#define ENABLE_DIGITAL_PLL                // 启用数字锁相环
#define ENABLE_PLL_PROTECTION             // 启用保护系统
#define ENABLE_PLL_DIAGNOSTICS            // 启用诊断功能
#define ENABLE_PLL_AUTO_RECOVERY          // 启用自动恢复
#define ENABLE_PLL_DEBUG_OUTPUT           // 启用调试输出

// PLL参数配置
#define PLL_DEFAULT_KP 0.1f               // 默认比例增益
#define PLL_DEFAULT_KI 0.01f              // 默认积分增益
#define PLL_DEFAULT_KD 0.001f             // 默认微分增益
#define PLL_DEFAULT_PWM_CHANNEL 5         // 默认PWM通道

// 保护系统配置
#define PROTECTION_DEFAULT_LEVEL PROTECTION_ENHANCED
#define PHASE_LOSS_THRESHOLD 0.1f         // 相位失联阈值
#define POWER_DRIFT_THRESHOLD 5.0f        // 功率漂移阈值
#define FREQ_DEVIATION_THRESHOLD 0.5f     // 频率偏差阈值

// 测试配置
#define TEST_POWER_LEVELS {0, 10, 25, 50, 75, 100}  // 测试功率级别
#define TEST_DURATION_MS 5000             // 每个测试持续时间
#define TEST_STABILITY_THRESHOLD 95.0f    // 稳定性测试阈值

/**
 * 数字锁相环配置管理器
 * 管理系统配置参数和运行时调整
 */
class DigitalPLLConfig {
private:
    // PLL参数
    float pll_kp = PLL_DEFAULT_KP;
    float pll_ki = PLL_DEFAULT_KI;
    float pll_kd = PLL_DEFAULT_KD;
    int pwm_channel = PLL_DEFAULT_PWM_CHANNEL;
    
    // 保护参数
    ProtectionLevel protection_level = PROTECTION_DEFAULT_LEVEL;
    float phase_loss_threshold = PHASE_LOSS_THRESHOLD;
    float power_drift_threshold = POWER_DRIFT_THRESHOLD;
    float freq_deviation_threshold = FREQ_DEVIATION_THRESHOLD;
    
    // 系统开关
    bool pll_enabled = true;
    bool protection_enabled = true;
    bool diagnostics_enabled = true;
    bool auto_recovery_enabled = true;
    bool debug_output_enabled = true;
    
    // 配置状态
    bool config_loaded = false;
    bool config_modified = false;
    
public:
    DigitalPLLConfig();
    
    // 配置加载和保存
    bool loadConfig();
    bool saveConfig();
    void resetToDefaults();
    
    // PLL参数配置
    void setPLLParameters(float kp, float ki, float kd);
    void getPLLParameters(float& kp, float& ki, float& kd);
    void setPWMChannel(int channel);
    int getPWMChannel();
    
    // 保护参数配置
    void setProtectionLevel(ProtectionLevel level);
    ProtectionLevel getProtectionLevel();
    void setProtectionThresholds(float phase_loss, float power_drift, float freq_deviation);
    void getProtectionThresholds(float& phase_loss, float& power_drift, float& freq_deviation);
    
    // 系统开关配置
    void enablePLL(bool enable);
    void enableProtection(bool enable);
    void enableDiagnostics(bool enable);
    void enableAutoRecovery(bool enable);
    void enableDebugOutput(bool enable);
    
    // 状态查询
    bool isPLLEnabled();
    bool isProtectionEnabled();
    bool isDiagnosticsEnabled();
    bool isAutoRecoveryEnabled();
    bool isDebugOutputEnabled();
    bool isConfigModified();
    
    // 配置应用
    bool applyConfig(DigitalPLLIntegration* pll_system);
    
    // 配置显示
    void printConfig();
    void printConfigSummary();
};

/**
 * 数字锁相环测试套件
 * 提供全面的系统测试功能
 */
class DigitalPLLTestSuite {
private:
    DigitalPLLIntegration* pll_system;
    DigitalPLLConfig* config;
    
    // 测试状态
    bool test_running = false;
    int current_test = 0;
    unsigned long test_start_time = 0;
    
    // 测试结果
    struct TestResult {
        int power_level;
        float stability_score;
        float avg_variance;
        bool pll_locked;
        bool test_passed;
        unsigned long test_duration;
    };
    
    TestResult test_results[10];
    int test_count = 0;
    
    // 测试统计
    int tests_passed = 0;
    int tests_failed = 0;
    float overall_score = 0.0f;
    
public:
    DigitalPLLTestSuite(DigitalPLLIntegration* system, DigitalPLLConfig* cfg);
    
    // 测试控制
    bool startTestSuite();
    void stopTestSuite();
    bool isTestRunning();
    void updateTest();
    
    // 单项测试
    bool testPowerStability(int power_level, unsigned long duration_ms);
    bool testPLLLocking();
    bool testPhaseRecovery();
    bool testProtectionSystem();
    bool testFrequencyTracking();
    
    // 压力测试
    bool stressTestPowerChanges();
    bool stressTestFrequencyVariation();
    bool stressTestProtectionTriggers();
    
    // 结果分析
    void analyzeResults();
    void printTestResults();
    void printDetailedReport();
    float getOverallScore();
    
    // 自动优化
    bool autoOptimizePLLParameters();
    bool autoOptimizeProtectionThresholds();
    
private:
    // 测试辅助函数
    void initializeTest();
    void recordTestResult(int power, float score, float variance, bool locked, bool passed, unsigned long duration);
    bool evaluateStability(float threshold);
    void generateTestReport();
};

/**
 * 数字锁相环性能监测器
 * 实时监测系统性能指标
 */
class DigitalPLLPerformanceMonitor {
private:
    DigitalPLLIntegration* pll_system;
    
    // 性能指标
    float avg_stability_score = 0.0f;
    float avg_lock_time = 0.0f;
    float avg_recovery_time = 0.0f;
    int lock_success_rate = 0;
    int recovery_success_rate = 0;
    
    // 监测历史
    float stability_history[100];
    int history_index = 0;
    
    // 统计数据
    unsigned long total_runtime = 0;
    int total_power_changes = 0;
    int total_lock_attempts = 0;
    int total_recovery_attempts = 0;
    
    // 监测间隔
    unsigned long last_update = 0;
    unsigned long update_interval = 1000;  // 1秒更新一次
    
public:
    DigitalPLLPerformanceMonitor(DigitalPLLIntegration* system);
    
    // 监测控制
    void begin();
    void update();
    void reset();
    
    // 性能指标获取
    float getAverageStabilityScore();
    float getAverageLockTime();
    float getAverageRecoveryTime();
    int getLockSuccessRate();
    int getRecoverySuccessRate();
    
    // 性能报告
    void printPerformanceReport();
    void printTrendAnalysis();
    
    // 性能评估
    float calculatePerformanceScore();
    bool isPerformanceAcceptable();
    
    // 优化建议
    void generateOptimizationSuggestions();
    
private:
    void updateStatistics();
    void analyzePerformanceTrends();
};

// 全局配置和测试接口
extern DigitalPLLConfig* g_pll_config;
extern DigitalPLLTestSuite* g_pll_test_suite;
extern DigitalPLLPerformanceMonitor* g_pll_performance;

// 便捷函数
bool initializePLLSystem();
void updatePLLSystem();
void shutdownPLLSystem();

// 命令行接口
void handlePLLConfigCommand(String command);
void handlePLLTestCommand(String command);
void handlePLLPerformanceCommand(String command);

// 配置文件操作
bool loadPLLConfigFromFile(const char* filename);
bool savePLLConfigToFile(const char* filename);

#endif // DIGITAL_PLL_CONFIG_H
