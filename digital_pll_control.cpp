// digital_pll_control.cpp
// 数字锁相环相位控制系统实现
#include "digital_pll_control.h"

DigitalPLLControl::DigitalPLLControl(int channel) {
    pwm_channel = channel;
    
    // 初始化功率历史记录
    for (int i = 0; i < POWER_STABILITY_WINDOW; i++) {
        power_history[i] = 0.0f;
    }
    
    // 初始化时间戳
    pll_start_time = micros();
    last_update_time = pll_start_time;
    last_stable_time = pll_start_time;
}

void DigitalPLLControl::begin() {
    // 配置PWM通道
    ledcAttachChannel(pwm_channel, pwm_frequency, pwm_resolution, pwm_channel);
    
    // 初始化线性化查找表
    initializeLinearizationTable();
    
    // 重置锁相环状态
    reset();
    
    Serial.println("[DIGITAL_PLL] 数字锁相环初始化完成");
    Serial.printf("[DIGITAL_PLL] PWM通道:%d, 频率:%dHz, 分辨率:%d位\n", 
                  pwm_channel, pwm_frequency, pwm_resolution);
}

void DigitalPLLControl::update() {
    unsigned long current_time = micros();
    
    // 检查更新间隔
    if (current_time - last_update_time < PLL_UPDATE_INTERVAL_US) {
        return;
    }
    
    last_update_time = current_time;
    
    // 更新锁相环
    updatePLL();
    
    // 更新功率控制
    updatePowerControl();
    
    // 更新监测系统
    updatePowerMonitoring();
    updatePhaseDetection();
}

void DigitalPLLControl::updatePLL() {
    unsigned long current_time = micros();
    float dt = (current_time - last_update_time) / 1000000.0f; // 转换为秒
    
    // 更新数控振荡器相位
    updateVCO();
    
    // 计算相位误差
    phase_error = calculatePhaseError();
    
    // PID控制器计算频率校正
    float frequency_correction = pidController(phase_error);
    
    // 更新VCO频率
    vco_frequency = AC_NOMINAL_FREQUENCY + frequency_correction;
    
    // 限制频率范围
    vco_frequency = constrain(vco_frequency, 
                             AC_NOMINAL_FREQUENCY - AC_FREQUENCY_TOLERANCE,
                             AC_NOMINAL_FREQUENCY + AC_FREQUENCY_TOLERANCE);
    
    // 检查锁定状态
    if (abs(phase_error) < PLL_LOCK_THRESHOLD) {
        if (!pll_locked) {
            pll_locked = true;
            Serial.printf("[DIGITAL_PLL] 锁相环已锁定，频率:%.3fHz, 相位误差:%.4f弧度\n", 
                         vco_frequency, phase_error);
        }
    } else if (abs(phase_error) > PLL_UNLOCK_THRESHOLD) {
        if (pll_locked) {
            pll_locked = false;
            Serial.printf("[DIGITAL_PLL] 锁相环失锁，相位误差:%.4f弧度\n", phase_error);
        }
    }
}

void DigitalPLLControl::updateVCO() {
    unsigned long current_time = micros();
    float dt = (current_time - last_update_time) / 1000000.0f;
    
    // 更新VCO相位
    vco_phase += 2.0f * PI * vco_frequency * dt;
    vco_phase = normalizePhase(vco_phase);
    
    // 检测过零点
    static float last_vco_phase = 0.0f;
    if (last_vco_phase > PI && vco_phase < PI) {
        // 检测到过零点
        last_zero_cross_time = current_time;
        
        // 更新参考相位（简化的电网相位估计）
        reference_phase = 0.0f;
    }
    last_vco_phase = vco_phase;
}

float DigitalPLLControl::calculatePhaseError() {
    // 计算VCO相位与参考相位的误差
    float error = reference_phase - vco_phase;
    
    // 归一化相位误差到[-π, π]范围
    while (error > PI) error -= 2.0f * PI;
    while (error < -PI) error += 2.0f * PI;
    
    return error;
}

float DigitalPLLControl::pidController(float error) {
    unsigned long current_time = micros();
    float dt = (current_time - last_update_time) / 1000000.0f;
    
    // 比例项
    float proportional = PLL_KP * error;
    
    // 积分项
    pid_integral += error * dt;
    pid_integral = constrain(pid_integral, -10.0f, 10.0f); // 防止积分饱和
    float integral = PLL_KI * pid_integral;
    
    // 微分项
    pid_derivative = (error - last_phase_error) / dt;
    float derivative = PLL_KD * pid_derivative;
    
    last_phase_error = error;
    
    return proportional + integral + derivative;
}

void DigitalPLLControl::updatePowerControl() {
    if (target_power_percent <= 0) {
        ledcWriteChannel(pwm_channel, 0);
        return;
    }
    
    // 计算触发角度
    current_trigger_angle = calculateTriggerAngle(target_power_percent);
    
    // 计算PWM值
    int pwm_value = calculatePWMValue();
    
    // 输出PWM
    ledcWriteChannel(pwm_channel, pwm_value);
}

float DigitalPLLControl::calculateTriggerAngle(int power_percent) {
    power_percent = constrain(power_percent, 0, 100);
    return linearization_table[power_percent];
}

int DigitalPLLControl::calculatePWMValue() {
    if (target_power_percent <= 0) {
        return 0;
    }
    
    if (target_power_percent >= 100) {
        return 4095;
    }
    
    // 基于触发角度计算PWM占空比
    float duty_cycle = (180.0f - current_trigger_angle) / 180.0f;
    
    // 如果锁相环未锁定，降低功率以确保安全
    if (!pll_locked) {
        duty_cycle *= 0.8f; // 降低到80%
    }
    
    return (int)(duty_cycle * 4095);
}

void DigitalPLLControl::updatePowerMonitoring() {
    // 记录当前功率到历史缓冲区
    power_history[power_history_index] = (float)target_power_percent;
    power_history_index = (power_history_index + 1) % POWER_STABILITY_WINDOW;
    
    // 计算功率方差
    power_variance = calculatePowerVariance();
    
    // 判断功率稳定性
    power_stable = (power_variance < POWER_DRIFT_THRESHOLD);
    
    if (!power_stable) {
        Serial.printf("[DIGITAL_PLL] 功率不稳定，方差:%.2f%%\n", power_variance);
    }
}

void DigitalPLLControl::updatePhaseDetection() {
    unsigned long current_time = micros();
    
    // 检测相位失联
    if (detectPhaseLoss()) {
        phase_loss_count++;
        
        if (!phase_loss_detected) {
            phase_loss_detected = true;
            Serial.println("[DIGITAL_PLL] 检测到相位失联，启动恢复程序");
            
            // 重置锁相环
            reset();
        }
    } else {
        // 重置失联计数
        if (phase_loss_detected && pll_locked) {
            phase_loss_detected = false;
            phase_loss_count = 0;
            Serial.println("[DIGITAL_PLL] 相位连接已恢复");
        }
        last_stable_time = current_time;
    }
}

bool DigitalPLLControl::detectPhaseLoss() {
    unsigned long current_time = micros();
    
    // 检查是否长时间未锁定
    if (!pll_locked && (current_time - pll_start_time) > PHASE_LOSS_DETECTION_TIME * 1000) {
        return true;
    }
    
    // 检查功率是否异常漂移
    if (!power_stable && power_variance > POWER_DRIFT_THRESHOLD * 2) {
        return true;
    }
    
    // 检查相位误差是否过大
    if (abs(phase_error) > PLL_UNLOCK_THRESHOLD * 2) {
        return true;
    }
    
    return false;
}

void DigitalPLLControl::setPower(int power_percent) {
    power_percent = constrain(power_percent, 0, 100);
    
    if (target_power_percent != power_percent) {
        Serial.printf("[DIGITAL_PLL] 功率设置: %d%% -> %d%%\n", 
                     target_power_percent, power_percent);
        target_power_percent = power_percent;
    }
}

int DigitalPLLControl::getCurrentPower() {
    return target_power_percent;
}

bool DigitalPLLControl::isPLLLocked() {
    return pll_locked;
}

bool DigitalPLLControl::isPowerStable() {
    return power_stable;
}

bool DigitalPLLControl::isPhaseConnected() {
    return !phase_loss_detected;
}

float DigitalPLLControl::getVCOFrequency() {
    return vco_frequency;
}

float DigitalPLLControl::getPhaseError() {
    return phase_error;
}

float DigitalPLLControl::getPowerVariance() {
    return power_variance;
}

void DigitalPLLControl::reset() {
    // 重置锁相环状态
    vco_frequency = AC_NOMINAL_FREQUENCY;
    vco_phase = 0.0f;
    reference_phase = 0.0f;
    phase_error = 0.0f;
    pll_locked = false;
    
    // 重置PID控制器
    pid_integral = 0.0f;
    pid_derivative = 0.0f;
    last_phase_error = 0.0f;
    
    // 重置时间戳
    pll_start_time = micros();
    last_update_time = pll_start_time;
    
    // 重置监测状态
    power_stable = true;
    phase_loss_detected = false;
    phase_loss_count = 0;
    
    Serial.println("[DIGITAL_PLL] 锁相环已重置");
}

void DigitalPLLControl::initializeLinearizationTable() {
    // 初始化功率线性化查找表
    for (int i = 0; i <= 100; i++) {
        if (i == 0) {
            linearization_table[i] = 180.0f; // 0%功率 = 180度延迟
        } else if (i == 100) {
            linearization_table[i] = 0.0f;   // 100%功率 = 0度延迟
        } else {
            // 使用反余弦函数实现线性化
            float power_ratio = i / 100.0f;
            linearization_table[i] = acos(2.0f * power_ratio - 1.0f) * 180.0f / PI;
        }
    }
    
    Serial.println("[DIGITAL_PLL] 功率线性化表初始化完成");
}

float DigitalPLLControl::normalizePhase(float phase) {
    while (phase > 2.0f * PI) phase -= 2.0f * PI;
    while (phase < 0.0f) phase += 2.0f * PI;
    return phase;
}

float DigitalPLLControl::calculatePowerVariance() {
    float sum = 0.0f;
    float mean = 0.0f;
    
    // 计算平均值
    for (int i = 0; i < POWER_STABILITY_WINDOW; i++) {
        mean += power_history[i];
    }
    mean /= POWER_STABILITY_WINDOW;
    
    // 计算方差
    for (int i = 0; i < POWER_STABILITY_WINDOW; i++) {
        float diff = power_history[i] - mean;
        sum += diff * diff;
    }
    
    return sqrt(sum / POWER_STABILITY_WINDOW);
}

void DigitalPLLControl::printDebugInfo() {
    Serial.println("========== 数字锁相环状态 ==========");
    Serial.printf("锁相环状态: %s\n", pll_locked ? "已锁定" : "未锁定");
    Serial.printf("VCO频率: %.3f Hz\n", vco_frequency);
    Serial.printf("相位误差: %.4f 弧度 (%.2f度)\n", phase_error, phase_error * 180.0f / PI);
    Serial.printf("目标功率: %d%%\n", target_power_percent);
    Serial.printf("触发角度: %.1f度\n", current_trigger_angle);
    Serial.printf("功率稳定性: %s (方差:%.2f%%)\n", power_stable ? "稳定" : "不稳定", power_variance);
    Serial.printf("相位连接: %s\n", phase_loss_detected ? "失联" : "正常");
    Serial.printf("失联次数: %d\n", phase_loss_count);
    Serial.println("=====================================");
}

// ============================================================================
// PLLDiagnostics 类实现
// ============================================================================

PLLDiagnostics::PLLDiagnostics(DigitalPLLControl* controller) {
    pll_controller = controller;
    resetStatistics();
}

void PLLDiagnostics::update() {
    if (!pll_controller) return;

    total_updates++;

    // 统计锁定状态
    static bool last_lock_state = false;
    bool current_lock_state = pll_controller->isPLLLocked();

    if (last_lock_state && !current_lock_state) {
        lock_loss_count++;
    }
    last_lock_state = current_lock_state;

    // 统计相位失联
    static bool last_phase_state = true;
    bool current_phase_state = pll_controller->isPhaseConnected();

    if (last_phase_state && !current_phase_state) {
        phase_loss_count++;
    }
    last_phase_state = current_phase_state;

    // 统计功率漂移
    if (!pll_controller->isPowerStable()) {
        power_drift_count++;
    }

    // 更新最大相位误差
    float current_phase_error = abs(pll_controller->getPhaseError());
    if (current_phase_error > max_phase_error) {
        max_phase_error = current_phase_error;
    }

    // 更新平均功率稳定性
    float current_variance = pll_controller->getPowerVariance();
    avg_power_stability = (avg_power_stability * 0.99f) + (current_variance * 0.01f);
}

void PLLDiagnostics::printReport() {
    Serial.println("========== 数字锁相环诊断报告 ==========");
    Serial.printf("总更新次数: %lu\n", total_updates);
    Serial.printf("锁定成功率: %.2f%%\n", getLockSuccessRate());
    Serial.printf("失锁次数: %lu\n", lock_loss_count);
    Serial.printf("相位失联次数: %lu\n", phase_loss_count);
    Serial.printf("功率漂移次数: %lu\n", power_drift_count);
    Serial.printf("最大相位误差: %.4f 弧度 (%.2f度)\n",
                  max_phase_error, max_phase_error * 180.0f / PI);
    Serial.printf("平均功率稳定性: %.2f%%\n", avg_power_stability);
    Serial.printf("功率稳定性评分: %.1f/10\n", getPowerStabilityScore());
    Serial.println("========================================");
}

void PLLDiagnostics::resetStatistics() {
    total_updates = 0;
    lock_loss_count = 0;
    phase_loss_count = 0;
    power_drift_count = 0;
    avg_lock_time = 0.0f;
    max_phase_error = 0.0f;
    avg_power_stability = 0.0f;
}

float PLLDiagnostics::getLockSuccessRate() {
    if (total_updates == 0) return 0.0f;
    return ((float)(total_updates - lock_loss_count) / total_updates) * 100.0f;
}

float PLLDiagnostics::getAverageLockTime() {
    return avg_lock_time;
}

float PLLDiagnostics::getPowerStabilityScore() {
    // 基于功率稳定性计算评分（0-10分）
    if (avg_power_stability < 1.0f) return 10.0f;
    if (avg_power_stability < 2.0f) return 9.0f;
    if (avg_power_stability < 3.0f) return 8.0f;
    if (avg_power_stability < 5.0f) return 7.0f;
    if (avg_power_stability < 8.0f) return 6.0f;
    if (avg_power_stability < 12.0f) return 5.0f;
    if (avg_power_stability < 18.0f) return 4.0f;
    if (avg_power_stability < 25.0f) return 3.0f;
    if (avg_power_stability < 35.0f) return 2.0f;
    if (avg_power_stability < 50.0f) return 1.0f;
    return 0.0f;
}
