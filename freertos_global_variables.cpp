// freertos_global_variables.cpp
// 全局变量定义文件
#include "freertos_architecture_design.h"

// ============================================================================
// 系统状态全局变量
// ============================================================================
SystemState_t g_system_state = {0};

// ============================================================================
// PID参数全局变量
// ============================================================================
float Kp = 2.0f;    // 比例增益
float Ki = 0.5f;    // 积分增益
float Kd = 0.1f;    // 微分增益

// ============================================================================
// 传感器配置全局变量
// ============================================================================
SensorConfig_t sensor_config = {
    .thermocouple_offset = 0.0f,
    .thermocouple_scale = 1.0f,
    .env_temp_offset = 0.0f,
    .env_temp_scale = 1.0f,
    .calibration_enabled = true,
    .thermocouple_enabled = true,
    .env_temp_enabled = true,
    .pot_enabled = true,
    .pressure_enabled = false,
    .oversample_count = 4,
    .settling_time_us = 1000
};

// ============================================================================
// 文件系统状态全局变量
// ============================================================================
FilesystemStatus_t fs_status = {
    .sd_card_available = false,
    .spiffs_available = false,
    .spiffs_mounted = false,
    .free_space_kb = 0,
    .total_space_kb = 0,
    .spiffs_total = 0,
    .spiffs_used = 0,
    .sd_total = 0,
    .sd_used = 0,
    .log_files_count = 0,
    .config_version = 1
};

// ============================================================================
// 烘焙会话全局变量
// ============================================================================
RoastSession_t current_roast_session = {
    .session_id = 0,
    .start_time = 0,
    .end_time = 0,
    .start_temp = 0.0f,
    .end_temp = 0.0f,
    .max_temp = 0.0f,
    .max_bean_temp = 0.0f,
    .total_energy = 0.0f,
    .data_points = 0,
    .total_points = 0,
    .profile_name = "Auto",
    .active = false
};

// ============================================================================
// FreeRTOS对象句柄
// ============================================================================

// 任务句柄
TaskHandle_t xTaskSafetyMonitor = NULL;
TaskHandle_t xTaskPWMControl = NULL;
TaskHandle_t xTaskPIDController = NULL;
TaskHandle_t xTaskPhaseControl = NULL;
TaskHandle_t xTaskSensorManager = NULL;
TaskHandle_t xTaskFilterProcess = NULL;
TaskHandle_t xTaskSerialComm = NULL;
TaskHandle_t xTaskBLEComm = NULL;
TaskHandle_t xTaskDisplayMgr = NULL;
TaskHandle_t xTaskDataRecord = NULL;
TaskHandle_t xTaskFileManager = NULL;

// 队列句柄
QueueHandle_t xQueueSensorData = NULL;
QueueHandle_t xQueueControlCmd = NULL;
QueueHandle_t xQueueSerialMsg = NULL;
QueueHandle_t xQueueBLEMsg = NULL;
QueueHandle_t xQueueDisplayData = NULL;
QueueHandle_t xQueueLogData = NULL;

// 信号量句柄
SemaphoreHandle_t xMutexSPI = NULL;
SemaphoreHandle_t xMutexI2C = NULL;
SemaphoreHandle_t xMutexSerial = NULL;
SemaphoreHandle_t xMutexSharedData = NULL;

// 事件组句柄
EventGroupHandle_t xEventGroupSystem = NULL;

// 软件定时器句柄
TimerHandle_t xTimerWatchdog = NULL;
TimerHandle_t xTimerHeartbeat = NULL;
