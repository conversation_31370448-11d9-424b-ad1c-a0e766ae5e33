WARNING: library LiquidCrystal I2C claims to run on avr architecture(s) and may be incompatible with your current board which runs on esp32 architecture(s).
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp: In function 'void Task_Filter_Process(void*)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:39:33: error: invalid conversion from 'int' to 'DisplayData_t::<unnamed enum>' [-fpermissive]
   39 | #define STATUS_ROASTING         1
      |                                 ^
      |                                 |
      |                                 int
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:265:35: note: in expansion of macro 'STATUS_ROASTING'
  265 |             display_data.status = STATUS_ROASTING; // 需要根据实际状态设置
      |                                   ^~~~~~~~~~~~~~~
In file included from F:\coffee21-0710BEST\coffee_freertos_main\coffee_freertos_main.ino:18:
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.h:22:3: error: conflicting declaration 'typedef struct SensorConfig_t SensorConfig_t'
   22 | } SensorConfig_t;
      |   ^~~~~~~~~~~~~~
In file included from F:\coffee21-0710BEST\coffee_freertos_main\coffee_freertos_main.ino:14:
F:\coffee21-0710BEST\coffee_freertos_main\freertos_architecture_design.h:248:3: note: previous declaration as 'typedef struct SensorConfig_t SensorConfig_t'
  248 | } SensorConfig_t;
      |   ^~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp: In function 'bool init_data_recording()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:141:46: error: 'RoastSession' was not declared in this scope; did you mean 'RoastSession_t'?
  141 |     memset(&current_roast_session, 0, sizeof(RoastSession));
      |                                              ^~~~~~~~~~~~
      |                                              RoastSession_t
Multiple libraries were found for "SD.h"
  Used: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\3.2.1\libraries\SD
  Not used: C:\Users\<USER>\AppData\Local\Arduino15\libraries\SD
  Not used: C:\Users\<USER>\Documents\Arduino\libraries\SD
exit status 1

Compilation error: invalid conversion from 'int' to 'DisplayData_t::<unnamed enum>' [-fpermissive]