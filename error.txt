WARNING: library LiquidCrystal I2C claims to run on avr architecture(s) and may be incompatible with your current board which runs on esp32 architecture(s).
F:\coffee21-0710BEST\coffee_freertos_main\freertos_realtime_control.cpp: In function 'void Task_PWM_Control(void*)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_realtime_control.cpp:166:45: error: 'CMD_SET_POWER' was not declared in this scope
  166 |             if (control_cmd.command_type == CMD_SET_POWER) {
      |                                             ^~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:61:3: error: conflicting declaration 'SensorConfig sensor_config'
   61 | } sensor_config;
      |   ^~~~~~~~~~~~~
In file included from F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:3:
F:\coffee21-0710BEST\coffee_freertos_main\freertos_architecture_design.h:242:23: note: previous declaration as 'SensorConfig_t sensor_config'
  242 | extern SensorConfig_t sensor_config;
      |                       ^~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:68:7: error: redefinition of 'class KalmanFilter'
   68 | class KalmanFilter {
      |       ^~~~~~~~~~~~
In file included from F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:5:
F:\coffee21-0710BEST\coffee_freertos_main\FilterLib.h:38:7: note: previous definition of 'class KalmanFilter'
   38 | class KalmanFilter
      |       ^~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp: In function 'void Task_Sensor_Manager(void*)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:184:27: error: 'struct SensorConfig_t' has no member named 'thermocouple_enabled'; did you mean 'thermocouple_offset'?
  184 |         if (sensor_config.thermocouple_enabled) {
      |                           ^~~~~~~~~~~~~~~~~~~~
      |                           thermocouple_offset
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:206:27: error: 'struct SensorConfig_t' has no member named 'env_temp_enabled'
  206 |         if (sensor_config.env_temp_enabled) {
      |                           ^~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:221:27: error: 'struct SensorConfig_t' has no member named 'pot_enabled'
  221 |         if (sensor_config.pot_enabled) {
      |                           ^~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:229:27: error: 'struct SensorConfig_t' has no member named 'pressure_enabled'
  229 |         if (sensor_config.pressure_enabled) {
      |                           ^~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp: In function 'void Task_Filter_Process(void*)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:271:22: error: 'class KalmanFilter' has no member named 'setNoiseParams'; did you mean 'setParams'?
  271 |     bean_temp_kalman.setNoiseParams(0.1f, 0.5f);  // 调整噪声参数
      |                      ^~~~~~~~~~~~~~
      |                      setParams
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:272:21: error: 'class KalmanFilter' has no member named 'setNoiseParams'; did you mean 'setParams'?
  272 |     env_temp_kalman.setNoiseParams(0.05f, 0.3f);
      |                     ^~~~~~~~~~~~~~
      |                     setParams
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:39:33: error: expected unqualified-id before numeric constant
   39 | #define STATUS_ROASTING         1
      |                                 ^
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:320:50: note: in expansion of macro 'STATUS_ROASTING'
  320 |             display_data.status = DisplayData_t::STATUS_ROASTING; // 需要根据实际状态设置
      |                                                  ^~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp: In function 'float read_thermocouple()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:383:37: error: 'struct SensorConfig_t' has no member named 'settling_time_us'
  383 |     delayMicroseconds(sensor_config.settling_time_us);
      |                                     ^~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:388:43: error: 'struct SensorConfig_t' has no member named 'oversample_count'
  388 |     for (uint8_t i = 0; i < sensor_config.oversample_count; i++) {
      |                                           ^~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:392:31: error: 'struct SensorConfig_t' has no member named 'oversample_count'
  392 |     raw_data /= sensor_config.oversample_count;
      |                               ^~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:407:47: error: 'struct SensorConfig_t' has no member named 'thermocouple_scale'; did you mean 'thermocouple_offset'?
  407 |     temperature = temperature * sensor_config.thermocouple_scale + sensor_config.thermocouple_offset;
      |                                               ^~~~~~~~~~~~~~~~~~
      |                                               thermocouple_offset
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp: In function 'float read_env_temperature()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:424:47: error: 'struct SensorConfig_t' has no member named 'env_temp_scale'; did you mean 'env_temp_offset'?
  424 |     temperature = temperature * sensor_config.env_temp_scale + sensor_config.env_temp_offset;
      |                                               ^~~~~~~~~~~~~~
      |                                               env_temp_offset
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp: In function 'void handle_short_press_freertos()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:542:24: error: 'CMD_SET_POWER' was not declared in this scope
  542 |     cmd.command_type = CMD_SET_POWER;
      |                        ^~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp: In function 'void handle_long_press_freertos()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:557:24: error: 'CMD_STOP_ROAST' was not declared in this scope
  557 |     cmd.command_type = CMD_STOP_ROAST;
      |                        ^~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_system_init.cpp: In function 'bool System_Set_PWM_Power(float)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_system_init.cpp:413:24: error: 'CMD_SET_POWER' was not declared in this scope
  413 |     cmd.command_type = CMD_SET_POWER;
      |                        ^~~~~~~~~~~~~
In file included from F:\coffee21-0710BEST\coffee_freertos_main\coffee_freertos_main.ino:18:
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.h:22:3: error: conflicting declaration 'typedef struct SensorConfig_t SensorConfig_t'
   22 | } SensorConfig_t;
      |   ^~~~~~~~~~~~~~
In file included from F:\coffee21-0710BEST\coffee_freertos_main\coffee_freertos_main.ino:14:
F:\coffee21-0710BEST\coffee_freertos_main\freertos_architecture_design.h:240:3: note: previous declaration as 'typedef struct SensorConfig_t SensorConfig_t'
  240 | } SensorConfig_t;
      |   ^~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:15:8: error: conflicting declaration 'double Kp'
   15 | double Kp = 2.0, Ki = 0.5, Kd = 1.0;
      |        ^~
In file included from F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:3:
F:\coffee21-0710BEST\coffee_freertos_main\freertos_architecture_design.h:233:14: note: previous declaration as 'float Kp'
  233 | extern float Kp, Ki, Kd;
      |              ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:15:18: error: conflicting declaration 'double Ki'
   15 | double Kp = 2.0, Ki = 0.5, Kd = 1.0;
      |                  ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_architecture_design.h:233:18: note: previous declaration as 'float Ki'
  233 | extern float Kp, Ki, Kd;
      |                  ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:15:28: error: conflicting declaration 'double Kd'
   15 | double Kp = 2.0, Ki = 0.5, Kd = 1.0;
      |                            ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_architecture_design.h:233:22: note: previous declaration as 'float Kd'
  233 | extern float Kp, Ki, Kd;
      |                      ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp: In function 'void Task_PID_Controller(void*)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:79:26: error: 'CMD_SET_PID_PARAMS' was not declared in this scope
   79 |                     case CMD_SET_PID_PARAMS:
      |                          ^~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:87:26: error: 'CMD_START_ROAST' was not declared in this scope
   87 |                     case CMD_START_ROAST:
      |                          ^~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:94:26: error: 'CMD_STOP_ROAST' was not declared in this scope
   94 |                     case CMD_STOP_ROAST:
      |                          ^~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp: In function 'void execute_pid_control(const SensorData_t&, PIDControlData_t*)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:153:34: error: 'CMD_SET_POWER' was not declared in this scope
  153 |         power_cmd.command_type = CMD_SET_POWER;
      |                                  ^~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp: In function 'bool Control_Set_PID_Parameters(float, float, float)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:334:24: error: 'CMD_SET_PID_PARAMS' was not declared in this scope
  334 |     cmd.command_type = CMD_SET_PID_PARAMS;
      |                        ^~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp: In function 'bool Control_Start_Roast(float)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:345:24: error: 'CMD_START_ROAST' was not declared in this scope
  345 |     cmd.command_type = CMD_START_ROAST;
      |                        ^~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp: In function 'bool Control_Stop_Roast()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:356:24: error: 'CMD_STOP_ROAST' was not declared in this scope
  356 |     cmd.command_type = CMD_STOP_ROAST;
      |                        ^~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:40:3: error: conflicting declaration 'FileSystemStatus fs_status'
   40 | } fs_status;
      |   ^~~~~~~~~
In file included from F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:3:
F:\coffee21-0710BEST\coffee_freertos_main\freertos_architecture_design.h:252:27: note: previous declaration as 'FilesystemStatus_t fs_status'
  252 | extern FilesystemStatus_t fs_status;
      |                           ^~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:53:3: error: conflicting declaration 'RoastSession current_roast_session'
   53 | } current_roast_session;
      |   ^~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_architecture_design.h:265:23: note: previous declaration as 'RoastSession_t current_roast_session'
  265 | extern RoastSession_t current_roast_session;
      |                       ^~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp: In function 'bool init_data_recording()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:136:19: error: 'struct FilesystemStatus_t' has no member named 'spiffs_mounted'
  136 |         fs_status.spiffs_mounted = true;
      |                   ^~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:137:19: error: 'struct FilesystemStatus_t' has no member named 'spiffs_total'
  137 |         fs_status.spiffs_total = SPIFFS.totalBytes();
      |                   ^~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:138:19: error: 'struct FilesystemStatus_t' has no member named 'spiffs_used'
  138 |         fs_status.spiffs_used = SPIFFS.usedBytes();
      |                   ^~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:140:32: error: 'struct FilesystemStatus_t' has no member named 'spiffs_used'
  140 |                      fs_status.spiffs_used, fs_status.spiffs_total);
      |                                ^~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:140:55: error: 'struct FilesystemStatus_t' has no member named 'spiffs_total'
  140 |                      fs_status.spiffs_used, fs_status.spiffs_total);
      |                                                       ^~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:149:19: error: 'struct FilesystemStatus_t' has no member named 'sd_total'
  149 |         fs_status.sd_total = SD.totalBytes();
      |                   ^~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:150:19: error: 'struct FilesystemStatus_t' has no member named 'sd_used'
  150 |         fs_status.sd_used = SD.usedBytes();
      |                   ^~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:152:32: error: 'struct FilesystemStatus_t' has no member named 'sd_used'
  152 |                      fs_status.sd_used, fs_status.sd_total);
      |                                ^~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:152:51: error: 'struct FilesystemStatus_t' has no member named 'sd_total'
  152 |                      fs_status.sd_used, fs_status.sd_total);
      |                                                   ^~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp: In function 'void update_roast_session(const LogData_t*)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:220:31: error: 'struct RoastSession_t' has no member named 'start_temp'; did you mean 'start_time'?
  220 |         current_roast_session.start_temp = log_data->bean_temp;
      |                               ^~~~~~~~~~
      |                               start_time
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:221:31: error: 'struct RoastSession_t' has no member named 'max_temp'
  221 |         current_roast_session.max_temp = log_data->bean_temp;
      |                               ^~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:222:31: error: 'struct RoastSession_t' has no member named 'total_points'; did you mean 'data_points'?
  222 |         current_roast_session.total_points = 1;
      |                               ^~~~~~~~~~~~
      |                               data_points
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:223:38: error: 'struct RoastSession_t' has no member named 'profile_name'
  223 |         strcpy(current_roast_session.profile_name, "Auto");
      |                                      ^~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:231:31: error: 'struct RoastSession_t' has no member named 'end_temp'; did you mean 'end_time'?
  231 |         current_roast_session.end_temp = log_data->bean_temp;
      |                               ^~~~~~~~
      |                               end_time
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:245:31: error: 'struct RoastSession_t' has no member named 'total_points'; did you mean 'data_points'?
  245 |         current_roast_session.total_points++;
      |                               ^~~~~~~~~~~~
      |                               data_points
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:246:57: error: 'struct RoastSession_t' has no member named 'max_temp'
  246 |         if (log_data->bean_temp > current_roast_session.max_temp) {
      |                                                         ^~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:247:35: error: 'struct RoastSession_t' has no member named 'max_temp'
  247 |             current_roast_session.max_temp = log_data->bean_temp;
      |                                   ^~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp: In function 'void save_roast_session_summary()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:267:47: error: 'struct RoastSession_t' has no member named 'start_temp'; did you mean 'start_time'?
  267 |     doc["start_temp"] = current_roast_session.start_temp;
      |                                               ^~~~~~~~~~
      |                                               start_time
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:268:45: error: 'struct RoastSession_t' has no member named 'end_temp'; did you mean 'end_time'?
  268 |     doc["end_temp"] = current_roast_session.end_temp;
      |                                             ^~~~~~~~
      |                                             end_time
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:269:45: error: 'struct RoastSession_t' has no member named 'max_temp'
  269 |     doc["max_temp"] = current_roast_session.max_temp;
      |                                             ^~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:270:49: error: 'struct RoastSession_t' has no member named 'total_points'; did you mean 'data_points'?
  270 |     doc["total_points"] = current_roast_session.total_points;
      |                                                 ^~~~~~~~~~~~
      |                                                 data_points
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:271:49: error: 'struct RoastSession_t' has no member named 'profile_name'
  271 |     doc["profile_name"] = current_roast_session.profile_name;
      |                                                 ^~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp: In function 'void update_filesystem_status()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:339:19: error: 'struct FilesystemStatus_t' has no member named 'spiffs_mounted'
  339 |     if (fs_status.spiffs_mounted) {
      |                   ^~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:340:19: error: 'struct FilesystemStatus_t' has no member named 'spiffs_used'
  340 |         fs_status.spiffs_used = SPIFFS.usedBytes();
      |                   ^~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:343:48: error: 'struct FilesystemStatus_t' has no member named 'spiffs_used'
  343 |         float usage_percent = (float)fs_status.spiffs_used / fs_status.spiffs_total * 100.0f;
      |                                                ^~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:343:72: error: 'struct FilesystemStatus_t' has no member named 'spiffs_total'
  343 |         float usage_percent = (float)fs_status.spiffs_used / fs_status.spiffs_total * 100.0f;
      |                                                                        ^~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:350:19: error: 'struct FilesystemStatus_t' has no member named 'sd_used'
  350 |         fs_status.sd_used = SD.usedBytes();
      |                   ^~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp: In function 'bool save_system_config()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:378:32: error: 'struct FilesystemStatus_t' has no member named 'config_version'
  378 |     doc["version"] = fs_status.config_version;
      |                                ^~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:390:51: error: 'struct SensorConfig_t' has no member named 'thermocouple_scale'; did you mean 'thermocouple_offset'?
  390 |     sensors["thermocouple_scale"] = sensor_config.thermocouple_scale;
      |                                                   ^~~~~~~~~~~~~~~~~~
      |                                                   thermocouple_offset
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:392:47: error: 'struct SensorConfig_t' has no member named 'env_temp_scale'; did you mean 'env_temp_offset'?
  392 |     sensors["env_temp_scale"] = sensor_config.env_temp_scale;
      |                                               ^~~~~~~~~~~~~~
      |                                               env_temp_offset
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp: In function 'bool load_system_config()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:444:23: error: 'struct SensorConfig_t' has no member named 'thermocouple_scale'; did you mean 'thermocouple_offset'?
  444 |         sensor_config.thermocouple_scale = doc["sensors"]["thermocouple_scale"] | 1.0f;
      |                       ^~~~~~~~~~~~~~~~~~
      |                       thermocouple_offset
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:446:23: error: 'struct SensorConfig_t' has no member named 'env_temp_scale'; did you mean 'env_temp_offset'?
  446 |         sensor_config.env_temp_scale = doc["sensors"]["env_temp_scale"] | 1.0f;
      |                       ^~~~~~~~~~~~~~
      |                       env_temp_offset
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:449:15: error: 'struct FilesystemStatus_t' has no member named 'config_version'
  449 |     fs_status.config_version = doc["version"] | 1;
      |               ^~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp: In function 'void ApplicationServices_Print_Status()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:462:28: error: 'struct FilesystemStatus_t' has no member named 'spiffs_mounted'
  462 |                  fs_status.spiffs_mounted ? "已挂载" : "未挂载",
      |                            ^~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:463:28: error: 'struct FilesystemStatus_t' has no member named 'spiffs_used'
  463 |                  fs_status.spiffs_used, fs_status.spiffs_total);
      |                            ^~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:463:51: error: 'struct FilesystemStatus_t' has no member named 'spiffs_total'
  463 |                  fs_status.spiffs_used, fs_status.spiffs_total);
      |                                                   ^~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:470:48: error: 'struct FilesystemStatus_t' has no member named 'config_version'
  470 |     Serial.printf("配置版本: %lu\n", fs_status.config_version);
      |                                                ^~~~~~~~~~~~~~
Multiple libraries were found for "SD.h"
  Used: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\3.2.1\libraries\SD
  Not used: C:\Users\<USER>\AppData\Local\Arduino15\libraries\SD
  Not used: C:\Users\<USER>\Documents\Arduino\libraries\SD
exit status 1

Compilation error: 'CMD_SET_POWER' was not declared in this scope