WARNING: library LiquidCrystal I2C claims to run on avr architecture(s) and may be incompatible with your current board which runs on esp32 architecture(s).
F:\coffee21-0710BEST\coffee_freertos_main\freertos_realtime_control.cpp: In function 'void Task_PWM_Control(void*)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_realtime_control.cpp:165:45: error: 'CMD_SET_POWER' was not declared in this scope
  165 |             if (control_cmd.command_type == CMD_SET_POWER) {
      |                                             ^~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_realtime_control.cpp: At global scope:
F:\coffee21-0710BEST\coffee_freertos_main\freertos_realtime_control.cpp:342:6: error: redefinition of 'bool RealTime_Check_Safety_Status()'
  342 | bool RealTime_Check_Safety_Status(void) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_realtime_control.cpp:257:6: note: 'bool RealTime_Check_Safety_Status()' previously defined here
  257 | bool RealTime_Check_Safety_Status(void) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_realtime_control.cpp: In function 'bool RealTime_Check_Safety_Status()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_realtime_control.cpp:343:28: error: 'struct SystemState_t' has no member named 'current_safety_status'
  343 |     return !g_system_state.current_safety_status.emergency_stop;
      |                            ^~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_realtime_control.cpp: At global scope:
F:\coffee21-0710BEST\coffee_freertos_main\freertos_realtime_control.cpp:347:6: error: redefinition of 'void RealTime_Print_Status()'
  347 | void RealTime_Print_Status(void) {
      |      ^~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_realtime_control.cpp:272:6: note: 'void RealTime_Print_Status()' previously defined here
  272 | void RealTime_Print_Status(void) {
      |      ^~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_system_init.cpp: In function 'bool System_Set_PWM_Power(float)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_system_init.cpp:413:24: error: 'CMD_SET_POWER' was not declared in this scope
  413 |     cmd.command_type = CMD_SET_POWER;
      |                        ^~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp: In function 'void Task_Sensor_Manager(void*)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:155:10: error: 'init_sensors' was not declared in this scope
  155 |     if (!init_sensors()) {
      |          ^~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:174:52: error: 'read_thermocouple' was not declared in this scope
  174 |                 raw_sensor_data.bean_temperature = read_thermocouple();
      |                                                    ^~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:195:47: error: 'read_env_temperature' was not declared in this scope
  195 |             raw_sensor_data.env_temperature = read_env_temperature();
      |                                               ^~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp: In function 'void Task_Filter_Process(void*)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:288:13: error: 'apply_sensor_calibration' was not declared in this scope
  288 |             apply_sensor_calibration(&filtered_data);
      |             ^~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:291:17: error: 'detect_sensor_anomalies' was not declared in this scope
  291 |             if (detect_sensor_anomalies(&filtered_data)) {
      |                 ^~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:306:42: error: 'RealTime_Get_Current_Power' was not declared in this scope
  306 |             display_data.power_percent = RealTime_Get_Current_Power();
      |                                          ^~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:27:33: error: invalid conversion from 'int' to 'DisplayData_t::<unnamed enum>' [-fpermissive]
   27 | #define STATUS_ROASTING         1
      |                                 ^
      |                                 |
      |                                 int
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:308:35: note: in expansion of macro 'STATUS_ROASTING'
  308 |             display_data.status = STATUS_ROASTING; // 需要根据实际状态设置
      |                                   ^~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:320:28: error: 'calculate_ror' was not declared in this scope
  320 |             log_data.ror = calculate_ror(filtered_data.bean_temperature);
      |                            ^~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp: In function 'bool init_sensors()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:358:23: error: 'read_thermocouple' was not declared in this scope
  358 |     float test_temp = read_thermocouple();
      |                       ^~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp: At global scope:
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:423:6: error: redefinition of 'bool init_sensors()'
  423 | bool init_sensors(void) {
      |      ^~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:341:6: note: 'bool init_sensors()' previously defined here
  341 | bool init_sensors(void) {
      |      ^~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:441:7: error: redefinition of 'float read_thermocouple()'
  441 | float read_thermocouple(void) {
      |       ^~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:368:7: note: 'float read_thermocouple()' previously defined here
  368 | float read_thermocouple(void) {
      |       ^~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:449:7: error: redefinition of 'float read_env_temperature()'
  449 | float read_env_temperature(void) {
      |       ^~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:400:7: note: 'float read_env_temperature()' previously defined here
  400 | float read_env_temperature(void) {
      |       ^~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp: In function 'void handle_short_press_freertos()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:560:24: error: 'CMD_SET_POWER' was not declared in this scope
  560 |     cmd.command_type = CMD_SET_POWER;
      |                        ^~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp: In function 'void handle_long_press_freertos()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:575:24: error: 'CMD_STOP_ROAST' was not declared in this scope
  575 |     cmd.command_type = CMD_STOP_ROAST;
      |                        ^~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp: At global scope:
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:588:6: error: redefinition of 'void apply_sensor_calibration(SensorData_t*)'
  588 | void apply_sensor_calibration(SensorData_t* data) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:455:6: note: 'void apply_sensor_calibration(SensorData_t*)' previously defined here
  455 | void apply_sensor_calibration(SensorData_t* data) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:622:10: error: ambiguating new declaration of 'uint16_t calculate_ror(float)'
  622 | uint16_t calculate_ror(float current_temp) {
      |          ^~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:474:7: note: old declaration 'float calculate_ror(float)'
  474 | float calculate_ror(float current_temp) {
      |       ^~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:643:6: error: redefinition of 'void DataProcessing_Print_Status()'
  643 | void DataProcessing_Print_Status(void) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:495:6: note: 'void DataProcessing_Print_Status()' previously defined here
  495 | void DataProcessing_Print_Status(void) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp: In function 'void Task_PID_Controller(void*)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:77:26: error: 'CMD_SET_PID_PARAMS' was not declared in this scope
   77 |                     case CMD_SET_PID_PARAMS:
      |                          ^~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:85:26: error: 'CMD_START_ROAST' was not declared in this scope
   85 |                     case CMD_START_ROAST:
      |                          ^~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:92:26: error: 'CMD_STOP_ROAST' was not declared in this scope
   92 |                     case CMD_STOP_ROAST:
      |                          ^~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:104:21: error: 'execute_pid_control' was not declared in this scope
  104 |                     execute_pid_control(sensor_data, &pid_data);
      |                     ^~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:108:21: error: 'execute_curve_following' was not declared in this scope
  108 |                     execute_curve_following(sensor_data, &pid_data);
      |                     ^~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:112:21: error: 'execute_adaptive_control' was not declared in this scope
  112 |                     execute_adaptive_control(sensor_data, &pid_data);
      |                     ^~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp: In function 'void execute_pid_control(const SensorData_t&, PIDControlData_t*)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:151:34: error: 'CMD_SET_POWER' was not declared in this scope
  151 |         power_cmd.command_type = CMD_SET_POWER;
      |                                  ^~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp: In function 'bool Control_Set_PID_Parameters(float, float, float)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:332:24: error: 'CMD_SET_PID_PARAMS' was not declared in this scope
  332 |     cmd.command_type = CMD_SET_PID_PARAMS;
      |                        ^~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp: In function 'bool Control_Start_Roast(float)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:343:24: error: 'CMD_START_ROAST' was not declared in this scope
  343 |     cmd.command_type = CMD_START_ROAST;
      |                        ^~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp: In function 'bool Control_Stop_Roast()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:354:24: error: 'CMD_STOP_ROAST' was not declared in this scope
  354 |     cmd.command_type = CMD_STOP_ROAST;
      |                        ^~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp: At global scope:
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:418:6: error: redefinition of 'void execute_pid_control(const SensorData_t&, PIDControlData_t*)'
  418 | void execute_pid_control(const SensorData_t& sensor_data, PIDControlData_t* pid_data) {
      |      ^~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:140:6: note: 'void execute_pid_control(const SensorData_t&, PIDControlData_t*)' previously defined here
  140 | void execute_pid_control(const SensorData_t& sensor_data, PIDControlData_t* pid_data) {
      |      ^~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp: In function 'void execute_pid_control(const SensorData_t&, PIDControlData_t*)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:428:34: error: 'CMD_SET_POWER' was not declared in this scope
  428 |         power_cmd.command_type = CMD_SET_POWER;
      |                                  ^~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp: At global scope:
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:439:6: error: redefinition of 'void execute_curve_following(const SensorData_t&, PIDControlData_t*)'
  439 | void execute_curve_following(const SensorData_t& sensor_data, PIDControlData_t* pid_data) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:167:6: note: 'void execute_curve_following(const SensorData_t&, PIDControlData_t*)' previously defined here
  167 | void execute_curve_following(const SensorData_t& sensor_data, PIDControlData_t* pid_data) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:445:6: error: redefinition of 'void execute_adaptive_control(const SensorData_t&, PIDControlData_t*)'
  445 | void execute_adaptive_control(const SensorData_t& sensor_data, PIDControlData_t* pid_data) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_control_algorithms.cpp:198:6: note: 'void execute_adaptive_control(const SensorData_t&, PIDControlData_t*)' previously defined here
  198 | void execute_adaptive_control(const SensorData_t& sensor_data, PIDControlData_t* pid_data) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\coffee_freertos_main.ino: In function 'void loop()':
F:\coffee21-0710BEST\coffee_freertos_main\coffee_freertos_main.ino:118:9: error: 'RealTime_Print_Status' was not declared in this scope
  118 |         RealTime_Print_Status();
      |         ^~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\coffee_freertos_main.ino: In function 'void handle_non_realtime_commands()':
F:\coffee21-0710BEST\coffee_freertos_main\coffee_freertos_main.ino:232:9: error: 'RealTime_Print_Status' was not declared in this scope
  232 |         RealTime_Print_Status();
      |         ^~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\coffee_freertos_main.ino:235:9: error: 'RealTime_Test_Safety_System' was not declared in this scope
  235 |         RealTime_Test_Safety_System();
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\coffee_freertos_main.ino:238:9: error: 'Control_Test_PID_Response' was not declared in this scope
  238 |         Control_Test_PID_Response();
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\coffee_freertos_main.ino:241:9: error: 'DataProcessing_Print_Status' was not declared in this scope
  241 |         DataProcessing_Print_Status();
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\coffee_freertos_main.ino:253:9: error: 'RealTime_Set_Emergency_Stop' was not declared in this scope; did you mean 'RealTime_Emergency_Stop'?
  253 |         RealTime_Set_Emergency_Stop(false);
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~
      |         RealTime_Emergency_Stop
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp: In function 'void Task_Serial_Comm(void*)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:76:21: error: 'process_serial_command' was not declared in this scope
   76 |                     process_serial_command(command_buffer);
      |                     ^~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:92:13: error: 'send_system_status_serial' was not declared in this scope
   92 |             send_system_status_serial();
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp: In function 'void process_serial_command(const char*)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:110:9: error: 'send_system_status_serial' was not declared in this scope
  110 |         send_system_status_serial();
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:119:9: error: 'RealTime_Print_Status' was not declared in this scope
  119 |         RealTime_Print_Status();
      |         ^~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:122:9: error: 'Control_Print_Status' was not declared in this scope
  122 |         Control_Print_Status();
      |         ^~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:125:9: error: 'DataProcessing_Print_Status' was not declared in this scope
  125 |         DataProcessing_Print_Status();
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:129:9: error: 'parse_pid_command' was not declared in this scope
  129 |         parse_pid_command(cmd);
      |         ^~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:140:9: error: 'Control_Start_Roast' was not declared in this scope
  140 |         Control_Start_Roast(temp);
      |         ^~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:144:9: error: 'Control_Start_Roast' was not declared in this scope
  144 |         Control_Start_Roast(200.0f); // 默认200°C
      |         ^~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:148:9: error: 'Control_Stop_Roast' was not declared in this scope
  148 |         Control_Stop_Roast();
      |         ^~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:156:9: error: 'RealTime_Set_Emergency_Stop' was not declared in this scope
  156 |         RealTime_Set_Emergency_Stop(false);
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:160:9: error: 'print_serial_help' was not declared in this scope
  160 |         print_serial_help();
      |         ^~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp: In function 'void parse_pid_command(const String&)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:178:47: error: 'Ki' was not declared in this scope
  178 |             Control_Set_PID_Parameters(value, Ki, Kd);
      |                                               ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:178:51: error: 'Kd' was not declared in this scope
  178 |             Control_Set_PID_Parameters(value, Ki, Kd);
      |                                                   ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:178:13: error: 'Control_Set_PID_Parameters' was not declared in this scope
  178 |             Control_Set_PID_Parameters(value, Ki, Kd);
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:182:40: error: 'Kp' was not declared in this scope
  182 |             Control_Set_PID_Parameters(Kp, value, Kd);
      |                                        ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:182:51: error: 'Kd' was not declared in this scope
  182 |             Control_Set_PID_Parameters(Kp, value, Kd);
      |                                                   ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:182:13: error: 'Control_Set_PID_Parameters' was not declared in this scope
  182 |             Control_Set_PID_Parameters(Kp, value, Kd);
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:186:40: error: 'Kp' was not declared in this scope
  186 |             Control_Set_PID_Parameters(Kp, Ki, value);
      |                                        ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:186:44: error: 'Ki' was not declared in this scope
  186 |             Control_Set_PID_Parameters(Kp, Ki, value);
      |                                            ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:186:13: error: 'Control_Set_PID_Parameters' was not declared in this scope
  186 |             Control_Set_PID_Parameters(Kp, Ki, value);
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp: In function 'void send_system_status_serial()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:205:41: error: 'RealTime_Get_Current_Power' was not declared in this scope
  205 |     Serial.printf("当前功率: %.1f%%\n", RealTime_Get_Current_Power());
      |                                         ^~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:206:37: error: 'RealTime_Check_Safety_Status' was not declared in this scope
  206 |     Serial.printf("安全状态: %s\n", RealTime_Check_Safety_Status() ? "正常" : "紧急停止");
      |                                     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp: In function 'void Task_BLE_Comm(void*)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:312:17: error: 'send_welcome_message_ble' was not declared in this scope
  312 |                 send_welcome_message_ble();
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:326:13: error: 'send_data_notification_ble' was not declared in this scope
  326 |             send_data_notification_ble();
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp: In function 'void send_json_status_ble()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:448:17: error: 'RealTime_Get_Current_Power' was not declared in this scope
  448 |                 RealTime_Get_Current_Power(),
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp: In function 'void send_data_notification_ble()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:469:17: error: 'RealTime_Get_Current_Power' was not declared in this scope
  469 |                 RealTime_Get_Current_Power(),   // FIR
      |                 ^~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp: In function 'void Task_Display_Mgr(void*)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:489:10: error: 'init_display' was not declared in this scope
  489 |     if (!init_display()) {
      |          ^~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:506:21: error: 'display_main_page' was not declared in this scope; did you mean 'display_page'?
  506 |                     display_main_page(&display_data);
      |                     ^~~~~~~~~~~~~~~~~
      |                     display_page
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:510:21: error: 'display_system_page' was not declared in this scope; did you mean 'display_page'?
  510 |                     display_system_page();
      |                     ^~~~~~~~~~~~~~~~~~~
      |                     display_page
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:514:21: error: 'display_pid_page' was not declared in this scope; did you mean 'display_page'?
  514 |                     display_pid_page();
      |                     ^~~~~~~~~~~~~~~~
      |                     display_page
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:518:21: error: 'display_network_page' was not declared in this scope
  518 |                     display_network_page();
      |                     ^~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:534:9: error: 'update_status_indicators' was not declared in this scope
  534 |         update_status_indicators();
      |         ^~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp: In function 'void display_pid_page()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:584:72: error: 'Kp' was not declared in this scope
  584 |         Serial.printf("[DISPLAY] PID页面 - Kp:%.2f Ki:%.2f Kd:%.2f\n", Kp, Ki, Kd);
      |                                                                        ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:584:76: error: 'Ki' was not declared in this scope
  584 |         Serial.printf("[DISPLAY] PID页面 - Kp:%.2f Ki:%.2f Kd:%.2f\n", Kp, Ki, Kd);
      |                                                                            ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:584:80: error: 'Kd' was not declared in this scope
  584 |         Serial.printf("[DISPLAY] PID页面 - Kp:%.2f Ki:%.2f Kd:%.2f\n", Kp, Ki, Kd);
      |                                                                                ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp: In function 'void update_status_indicators()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:608:22: error: 'RealTime_Check_Safety_Status' was not declared in this scope
  608 |     bool safety_ok = RealTime_Check_Safety_Status();
      |                      ^~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp: At global scope:
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:642:6: error: redefinition of 'void send_system_status_serial()'
  642 | void send_system_status_serial() {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:192:6: note: 'void send_system_status_serial()' previously defined here
  192 | void send_system_status_serial(void) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:646:6: error: redefinition of 'void parse_pid_command(const String&)'
  646 | void parse_pid_command(const String& cmd) {
      |      ^~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:168:6: note: 'void parse_pid_command(const String&)' previously defined here
  168 | void parse_pid_command(const String& cmd) {
      |      ^~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:650:6: error: redefinition of 'void print_serial_help()'
  650 | void print_serial_help() {
      |      ^~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:212:6: note: 'void print_serial_help()' previously defined here
  212 | void print_serial_help(void) {
      |      ^~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:658:6: error: redefinition of 'bool init_display()'
  658 | bool init_display() {
      |      ^~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:540:6: note: 'bool init_display()' previously defined here
  540 | bool init_display(void) {
      |      ^~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:663:6: error: redefinition of 'void display_main_page(const DisplayData_t*)'
  663 | void display_main_page(const DisplayData_t* data) {
      |      ^~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:556:6: note: 'void display_main_page(const DisplayData_t*)' previously defined here
  556 | void display_main_page(const DisplayData_t* data) {
      |      ^~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:668:6: error: redefinition of 'void display_system_page()'
  668 | void display_system_page() {
      |      ^~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:571:6: note: 'void display_system_page()' previously defined here
  571 | void display_system_page(void) {
      |      ^~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:672:6: error: redefinition of 'void display_pid_page()'
  672 | void display_pid_page() {
      |      ^~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:581:6: note: 'void display_pid_page()' previously defined here
  581 | void display_pid_page(void) {
      |      ^~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp: In function 'void display_pid_page()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:673:68: error: 'Kp' was not declared in this scope
  673 |     Serial.printf("[DISPLAY] PID页面 - Kp:%.2f Ki:%.2f Kd:%.2f\n", Kp, Ki, Kd);
      |                                                                    ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:673:72: error: 'Ki' was not declared in this scope
  673 |     Serial.printf("[DISPLAY] PID页面 - Kp:%.2f Ki:%.2f Kd:%.2f\n", Kp, Ki, Kd);
      |                                                                        ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:673:76: error: 'Kd' was not declared in this scope
  673 |     Serial.printf("[DISPLAY] PID页面 - Kp:%.2f Ki:%.2f Kd:%.2f\n", Kp, Ki, Kd);
      |                                                                            ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp: At global scope:
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:676:6: error: redefinition of 'void display_network_page()'
  676 | void display_network_page() {
      |      ^~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:590:6: note: 'void display_network_page()' previously defined here
  590 | void display_network_page(void) {
      |      ^~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:680:6: error: redefinition of 'void update_status_indicators()'
  680 | void update_status_indicators() {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_communication.cpp:602:6: note: 'void update_status_indicators()' previously defined here
  602 | void update_status_indicators(void) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp: In function 'void Task_Data_Record(void*)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:61:10: error: 'init_data_recording' was not declared in this scope
   61 |     if (!init_data_recording()) {
      |          ^~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:86:21: error: 'write_log_buffer_to_file' was not declared in this scope
   86 |                 if (write_log_buffer_to_file(log_buffer, buffer_index)) {
      |                     ^~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:92:21: error: 'update_roast_session' was not declared in this scope; did you mean 'current_roast_session'?
   92 |                     update_roast_session(&log_data);
      |                     ^~~~~~~~~~~~~~~~~~~~
      |                     current_roast_session
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:103:13: error: 'perform_file_cleanup' was not declared in this scope
  103 |             perform_file_cleanup();
      |             ^~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:110:13: error: 'update_filesystem_status' was not declared in this scope
  110 |             update_filesystem_status();
      |             ^~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp: In function 'bool init_data_recording()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:160:40: error: 'generate_session_id' was not declared in this scope
  160 |     current_roast_session.session_id = generate_session_id();
      |                                        ^~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp: In function 'bool write_log_buffer_to_file(const LogData_t*, uint16_t)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:175:54: error: conditional expression between distinct pointer types 'fs::SDFS*' and 'fs::SPIFFSFS*' lacks a cast
  175 |     fs::FS* filesystem = fs_status.sd_card_available ? &SD : &SPIFFS;
      |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp: In function 'void update_roast_session(const LogData_t*)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:229:9: error: 'save_roast_session_summary' was not declared in this scope
  229 |         save_roast_session_summary();
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:236:44: error: 'generate_session_id' was not declared in this scope
  236 |         current_roast_session.session_id = generate_session_id();
      |                                            ^~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp: In function 'void save_roast_session_summary()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:269:54: error: conditional expression between distinct pointer types 'fs::SDFS*' and 'fs::SPIFFSFS*' lacks a cast
  269 |     fs::FS* filesystem = fs_status.sd_card_available ? &SD : &SPIFFS;
      |                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp: In function 'void Task_File_Manager(void*)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:295:13: error: 'perform_file_maintenance' was not declared in this scope
  295 |             perform_file_maintenance();
      |             ^~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:302:13: error: 'check_config_file_changes' was not declared in this scope
  302 |             check_config_file_changes();
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp: In function 'bool save_system_config()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:378:17: error: 'Kp' was not declared in this scope
  378 |     pid["kp"] = Kp;
      |                 ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:379:17: error: 'Ki' was not declared in this scope
  379 |     pid["ki"] = Ki;
      |                 ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:380:17: error: 'Kd' was not declared in this scope
  380 |     pid["kd"] = Kd;
      |                 ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:384:38: error: 'sensor_config' was not declared in this scope; did you mean 'gpio_config'?
  384 |     sensors["thermocouple_offset"] = sensor_config.thermocouple_offset;
      |                                      ^~~~~~~~~~~~~
      |                                      gpio_config
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp: In function 'bool load_system_config()':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:430:9: error: 'Kp' was not declared in this scope
  430 |         Kp = doc["pid"]["kp"] | Kp;
      |         ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:431:9: error: 'Ki' was not declared in this scope
  431 |         Ki = doc["pid"]["ki"] | Ki;
      |         ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:432:9: error: 'Kd' was not declared in this scope
  432 |         Kd = doc["pid"]["kd"] | Kd;
      |         ^~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:433:9: error: 'Control_Set_PID_Parameters' was not declared in this scope
  433 |         Control_Set_PID_Parameters(Kp, Ki, Kd);
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:438:9: error: 'sensor_config' was not declared in this scope; did you mean 'gpio_config'?
  438 |         sensor_config.thermocouple_offset = doc["sensors"]["thermocouple_offset"] | 0.0f;
      |         ^~~~~~~~~~~~~
      |         gpio_config
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp: At global scope:
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:484:6: error: redefinition of 'bool init_data_recording()'
  484 | bool init_data_recording() {
      |      ^~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:126:6: note: 'bool init_data_recording()' previously defined here
  126 | bool init_data_recording(void) {
      |      ^~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:489:6: error: redefinition of 'bool write_log_buffer_to_file(const LogData_t*, uint16_t)'
  489 | bool write_log_buffer_to_file(const LogData_t* buffer, uint16_t count) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:166:6: note: 'bool write_log_buffer_to_file(const LogData_t*, uint16_t)' previously defined here
  166 | bool write_log_buffer_to_file(const LogData_t* buffer, uint16_t count) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:494:6: error: redefinition of 'void update_roast_session(const LogData_t*)'
  494 | void update_roast_session(const LogData_t* data) {
      |      ^~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:206:6: note: 'void update_roast_session(const LogData_t*)' previously defined here
  206 | void update_roast_session(const LogData_t* log_data) {
      |      ^~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:498:6: error: redefinition of 'void perform_file_cleanup()'
  498 | void perform_file_cleanup() {
      |      ^~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:309:6: note: 'void perform_file_cleanup()' previously defined here
  309 | void perform_file_cleanup(void) {
      |      ^~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:502:6: error: redefinition of 'void update_filesystem_status()'
  502 | void update_filesystem_status() {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:333:6: note: 'void update_filesystem_status()' previously defined here
  333 | void update_filesystem_status(void) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:506:10: error: redefinition of 'uint32_t generate_session_id()'
  506 | uint32_t generate_session_id() {
      |          ^~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:247:10: note: 'uint32_t generate_session_id()' previously defined here
  247 | uint32_t generate_session_id(void) {
      |          ^~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:510:6: error: redefinition of 'void save_roast_session_summary()'
  510 | void save_roast_session_summary() {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:251:6: note: 'void save_roast_session_summary()' previously defined here
  251 | void save_roast_session_summary(void) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:514:6: error: redefinition of 'void perform_file_maintenance()'
  514 | void perform_file_maintenance() {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:349:6: note: 'void perform_file_maintenance()' previously defined here
  349 | void perform_file_maintenance(void) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:518:6: error: redefinition of 'void check_config_file_changes()'
  518 | void check_config_file_changes() {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~~
F:\coffee21-0710BEST\coffee_freertos_main\freertos_application_services.cpp:360:6: note: 'void check_config_file_changes()' previously defined here
  360 | void check_config_file_changes(void) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~~
Multiple libraries were found for "SD.h"
  Used: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\3.2.1\libraries\SD
  Not used: C:\Users\<USER>\AppData\Local\Arduino15\libraries\SD
  Not used: C:\Users\<USER>\Documents\Arduino\libraries\SD
exit status 1

Compilation error: 'CMD_SET_POWER' was not declared in this scope