WARNING: library LiquidCrystal I2C claims to run on avr architecture(s) and may be incompatible with your current board which runs on esp32 architecture(s).
In file included from F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:4:
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp: In function 'void Task_Filter_Process(void*)':
F:\coffee21-0710BEST\coffee_freertos_main\freertos_missing_definitions.h:46:29: error: expected unqualified-id before numeric constant
   46 | #define STATUS_ROASTING     2
      |                             ^
F:\coffee21-0710BEST\coffee_freertos_main\freertos_data_processing.cpp:263:50: note: in expansion of macro 'STATUS_ROASTING'
  263 |             display_data.status = DisplayData_t::STATUS_ROASTING; // 需要根据实际状态设置
      |                                                  ^~~~~~~~~~~~~~~
Multiple libraries were found for "SD.h"
  Used: C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\3.2.1\libraries\SD
  Not used: C:\Users\<USER>\AppData\Local\Arduino15\libraries\SD
  Not used: C:\Users\<USER>\Documents\Arduino\libraries\SD
exit status 1

Compilation error: expected unqualified-id before numeric constant