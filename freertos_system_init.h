#ifndef FREERTOS_SYSTEM_INIT_H
#define FREERTOS_SYSTEM_INIT_H

#include "freertos_architecture_design.h"

// ============================================================================
// FreeRTOS系统初始化头文件
// ============================================================================

// 系统初始化函数
bool FreeRTOS_System_Init(void);
bool hardware_init(void);
bool create_freertos_tasks(void);
bool create_freertos_queues(void);
bool create_freertos_timers(void);
bool create_freertos_events(void);

// 系统状态检查
bool system_health_check(void);
void print_system_info(void);

#endif // FREERTOS_SYSTEM_INIT_H
