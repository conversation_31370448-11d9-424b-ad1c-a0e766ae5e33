# FreeRTOS任务设计详细规范

## 系统架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                    FreeRTOS咖啡烘焙机控制系统                    │
├─────────────────────────────────────────────────────────────┤
│  实时控制层 (优先级4-5)                                        │
│  ├── Task_Safety_Monitor    (50Hz, 优先级5)                  │
│  └── Task_PWM_Control       (100Hz, 优先级4)                 │
├─────────────────────────────────────────────────────────────┤
│  控制算法层 (优先级3)                                          │
│  ├── Task_PID_Controller    (10Hz, 优先级3)                  │
│  └── Task_Phase_Control     (20Hz, 优先级3)                  │
├─────────────────────────────────────────────────────────────┤
│  数据处理层 (优先级2)                                          │
│  ├── Task_Sensor_Manager    (5Hz, 优先级2)                   │
│  └── Task_Filter_Process    (10Hz, 优先级2)                  │
├─────────────────────────────────────────────────────────────┤
│  通信接口层 (优先级1)                                          │
│  ├── Task_Serial_Comm       (事件驱动, 优先级1)                │
│  ├── Task_BLE_Comm          (事件驱动, 优先级1)                │
│  └── Task_Display_Mgr       (1Hz, 优先级1)                   │
├─────────────────────────────────────────────────────────────┤
│  应用服务层 (优先级0)                                          │
│  ├── Task_Data_Record       (1Hz, 优先级0)                   │
│  └── Task_File_Manager      (事件驱动, 优先级0)                │
└─────────────────────────────────────────────────────────────┘
```

## 详细任务设计

### 🔥 实时控制层

#### Task_Safety_Monitor (优先级5, 50Hz)
**职责**: 系统安全监控，最高优先级
**输入**: 
- 传感器数据队列
- 系统状态
**输出**: 
- 紧急停止信号
- 安全事件
**关键功能**:
```cpp
void Task_Safety_Monitor(void *pvParameters) {
    TickType_t xLastWakeTime = xTaskGetTickCount();
    
    while(1) {
        // 1. 检查温度超限
        if (bean_temp > MAX_SAFE_TEMP) {
            System_Emergency_Stop();
        }
        
        // 2. 检查功率异常
        if (power_variance > POWER_VARIANCE_LIMIT) {
            // 触发功率保护
        }
        
        // 3. 检查通信超时
        if (sensor_timeout > SENSOR_TIMEOUT_LIMIT) {
            // 传感器故障处理
        }
        
        // 4. 看门狗喂狗
        xTimerReset(xTimerWatchdog, 0);
        
        vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(CYCLE_SAFETY_MONITOR));
    }
}
```

#### Task_PWM_Control (优先级4, 100Hz)
**职责**: 实时PWM输出控制
**输入**: 
- PID控制输出
- 安全覆盖信号
**输出**: 
- PWM硬件控制
**关键功能**:
```cpp
void Task_PWM_Control(void *pvParameters) {
    PWMControlData_t pwm_data;
    TickType_t xLastWakeTime = xTaskGetTickCount();
    
    while(1) {
        // 1. 获取控制指令
        if (xQueueReceive(xQueueControlCmd, &pwm_data, 0) == pdTRUE) {
            // 处理新的功率设置
        }
        
        // 2. 安全检查
        if (xEventGroupGetBits(xEventGroupSystem) & EVENT_BIT_EMERGENCY_STOP) {
            pwm_data.target_power = 0;
            pwm_data.safety_override = true;
        }
        
        // 3. 更新PWM输出
        pwmController.setPower(pwm_data.target_power);
        pwmController.update();
        
        // 4. 反馈当前状态
        pwm_data.timestamp = xTaskGetTickCount();
        // 更新共享状态
        
        vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(CYCLE_PWM_CONTROL));
    }
}
```

### ⚙️ 控制算法层

#### Task_PID_Controller (优先级3, 10Hz)
**职责**: PID控制算法计算
**输入**: 
- 滤波后的温度数据
- PID参数设置
**输出**: 
- 功率控制指令
**关键功能**:
```cpp
void Task_PID_Controller(void *pvParameters) {
    PIDControlData_t pid_data;
    SensorData_t sensor_data;
    ControlCommand_t control_cmd;
    TickType_t xLastWakeTime = xTaskGetTickCount();
    
    while(1) {
        // 1. 获取传感器数据
        if (xQueueReceive(xQueueSensorData, &sensor_data, pdMS_TO_TICKS(50)) == pdTRUE) {
            
            // 2. PID计算
            if (pid_data.auto_mode) {
                pid_data.input = sensor_data.bean_temperature;
                
                // 执行PID计算
                myPID.Compute();
                
                pid_data.output = Output;
                
                // 3. 发送控制指令
                control_cmd.command_type = CMD_SET_POWER;
                control_cmd.params.set_power.power_percent = pid_data.output;
                control_cmd.timestamp = xTaskGetTickCount();
                
                xQueueSend(xQueueControlCmd, &control_cmd, 0);
            }
        }
        
        vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(CYCLE_PID_CONTROLLER));
    }
}
```

#### Task_Phase_Control (优先级3, 20Hz)
**职责**: 数字锁相环相位控制
**输入**: 
- 功率反馈数据
- 相位检测信号
**输出**: 
- 相位校正指令
**关键功能**:
```cpp
void Task_Phase_Control(void *pvParameters) {
    TickType_t xLastWakeTime = xTaskGetTickCount();
    
    while(1) {
        // 1. 更新数字锁相环
        if (pll_system) {
            pll_system->update();
            
            // 2. 检查相位失联
            if (!pll_system->isPLLLocked()) {
                // 触发相位恢复
                pll_system->forceRecovery();
            }
            
            // 3. 功率漂移检测
            if (pll_system->getPowerVariance() > DRIFT_THRESHOLD) {
                // 发送漂移警告
            }
        }
        
        vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(CYCLE_PHASE_CONTROL));
    }
}
```

### 📊 数据处理层

#### Task_Sensor_Manager (优先级2, 5Hz)
**职责**: 传感器数据采集和管理
**输入**: 
- 硬件传感器
**输出**: 
- 原始传感器数据
**关键功能**:
```cpp
void Task_Sensor_Manager(void *pvParameters) {
    SensorData_t sensor_data;
    TickType_t xLastWakeTime = xTaskGetTickCount();
    
    while(1) {
        // 1. 获取SPI互斥锁
        if (xSemaphoreTake(xMutexSPI, pdMS_TO_TICKS(100)) == pdTRUE) {
            
            // 2. 读取豆温传感器
            sensor_data.bean_temperature = thermocouple.readCelsius();
            
            // 3. 读取环温传感器  
            sensor_data.env_temperature = env_sensor.readTemperature();
            
            xSemaphoreGive(xMutexSPI);
        }
        
        // 4. 读取电位器
        sensor_data.pot_value = analogRead(POT_PIN);
        
        // 5. 数据有效性检查
        sensor_data.valid = (sensor_data.bean_temperature > -50 && 
                           sensor_data.bean_temperature < 300);
        
        sensor_data.timestamp = xTaskGetTickCount();
        
        // 6. 发送到滤波处理队列
        xQueueSend(xQueueSensorData, &sensor_data, 0);
        
        vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(CYCLE_SENSOR_MANAGER));
    }
}
```

#### Task_Filter_Process (优先级2, 10Hz)
**职责**: 数据滤波和处理
**输入**: 
- 原始传感器数据
**输出**: 
- 滤波后的数据
**关键功能**:
```cpp
void Task_Filter_Process(void *pvParameters) {
    SensorData_t raw_data, filtered_data;
    TickType_t xLastWakeTime = xTaskGetTickCount();
    
    while(1) {
        // 1. 获取原始数据
        if (xQueueReceive(xQueueSensorData, &raw_data, pdMS_TO_TICKS(50)) == pdTRUE) {
            
            // 2. 卡尔曼滤波
            filtered_data.bean_temperature = kalmanFilter.updateEstimate(raw_data.bean_temperature);
            
            // 3. 移动平均滤波
            filtered_data.env_temperature = movingAverage.addValue(raw_data.env_temperature);
            
            // 4. 电位器去抖
            filtered_data.pot_value = debounceFilter.process(raw_data.pot_value);
            
            filtered_data.timestamp = xTaskGetTickCount();
            filtered_data.valid = raw_data.valid;
            
            // 5. 更新共享数据
            if (xSemaphoreTake(xMutexSharedData, pdMS_TO_TICKS(10)) == pdTRUE) {
                g_system_state.current_sensor_data = filtered_data;
                xSemaphoreGive(xMutexSharedData);
            }
        }
        
        vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(CYCLE_FILTER_PROCESS));
    }
}
```

### 📡 通信接口层

#### Task_Serial_Comm (优先级1, 事件驱动)
**职责**: 串口通信处理
**输入**: 
- 串口接收中断
**输出**: 
- 命令解析和响应
**关键功能**:
```cpp
void Task_Serial_Comm(void *pvParameters) {
    char serial_buffer[256];
    ControlCommand_t control_cmd;
    
    while(1) {
        // 1. 等待串口数据
        if (xQueueReceive(xQueueSerialMsg, serial_buffer, portMAX_DELAY) == pdTRUE) {
            
            // 2. 命令解析
            if (strncmp(serial_buffer, "PID;", 4) == 0) {
                // 解析PID命令
                parsePIDCommand(serial_buffer, &control_cmd);
                xQueueSend(xQueueControlCmd, &control_cmd, 0);
            }
            else if (strncmp(serial_buffer, "POWER;", 6) == 0) {
                // 解析功率命令
                parsePowerCommand(serial_buffer, &control_cmd);
                xQueueSend(xQueueControlCmd, &control_cmd, 0);
            }
            
            // 3. 发送响应
            if (xSemaphoreTake(xMutexSerial, pdMS_TO_TICKS(100)) == pdTRUE) {
                Serial.println("Command processed");
                xSemaphoreGive(xMutexSerial);
            }
        }
    }
}
```

## 数据流设计

```
传感器硬件 → Task_Sensor_Manager → xQueueSensorData → Task_Filter_Process
                                                            ↓
Task_PWM_Control ← xQueueControlCmd ← Task_PID_Controller ← 滤波数据
        ↓
    PWM硬件输出
```

## 同步机制设计

### 互斥锁使用
- **xMutexSPI**: 保护SPI总线访问（传感器读取）
- **xMutexI2C**: 保护I2C总线访问（显示屏等）
- **xMutexSerial**: 保护串口输出
- **xMutexSharedData**: 保护全局共享数据结构

### 事件组使用
- **EVENT_BIT_EMERGENCY_STOP**: 紧急停止事件
- **EVENT_BIT_ROAST_ACTIVE**: 烘焙激活状态
- **EVENT_BIT_SENSORS_READY**: 传感器就绪状态

### 队列通信
- **高频数据**: 传感器数据、控制指令
- **低频数据**: 显示数据、日志数据
- **事件消息**: 串口命令、BLE消息

## 内存和性能优化

### 栈大小优化
- **实时任务**: 2KB栈（精简代码）
- **通信任务**: 3-4KB栈（缓冲区需求）
- **数据处理**: 2-3KB栈（算法计算）

### CPU核心分配
- **Core 0**: 实时控制任务（PWM、安全监控）
- **Core 1**: 通信和数据处理任务

### 中断优化
- **高优先级中断**: 安全相关（温度超限）
- **中优先级中断**: 定时器、PWM
- **低优先级中断**: 串口、SPI

这个架构设计确保了：
1. **实时性**: 关键控制任务有最高优先级
2. **可靠性**: 多层安全保护和错误处理
3. **可扩展性**: 模块化设计便于添加新功能
4. **性能**: 充分利用ESP32双核和FreeRTOS特性
