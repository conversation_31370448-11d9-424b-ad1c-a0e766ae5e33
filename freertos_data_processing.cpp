// freertos_data_processing.cpp
// FreeRTOS数据处理层实现 - 传感器管理和滤波处理
#include "freertos_architecture_design.h"
#include "freertos_missing_definitions.h"
#include "FilterLib.h"
#include "user_config.h"
#include <SPI.h>
#include <Wire.h>

// 引脚定义
#define THERMOCOUPLE_SCK_PIN  18
#define THERMOCOUPLE_SO_PIN   19
#define THERMOCOUPLE_CS_PIN   4
#define ENV_TEMP_PIN          35

// 外部变量声明
extern double Input, Output, Setpoint;

// ============================================================================
// 传感器硬件接口定义
// ============================================================================

// MAX6675热电偶传感器
#define THERMOCOUPLE_CS_PIN     15
#define THERMOCOUPLE_SCK_PIN    14
#define THERMOCOUPLE_SO_PIN     12

// 环温传感器（假设使用DS18B20或类似）
#define ENV_TEMP_PIN            4

// 电位器和其他模拟输入
#define POT_PIN                 A0
#define PRESSURE_SENSOR_PIN     A1

// 按键输入
#define BUTTON_PIN              15  // 对应user_config.h中的BUTTON_BT

// 系统状态定义已在freertos_missing_definitions.h中定义，不重复定义

// 全局变量
extern double Output;  // PID输出变量

// 使用全局定义的传感器配置，不重复定义

// ============================================================================
// 滤波器实现
// ============================================================================

// 使用FilterLib.h中的KalmanFilter类，不重复定义

// 移动平均滤波器类
class MovingAverageFilter {
private:
    float* buffer;
    uint8_t size;
    uint8_t index = 0;
    float sum = 0.0f;
    bool filled = false;
    
public:
    MovingAverageFilter(uint8_t filter_size) : size(filter_size) {
        buffer = new float[size];
        for (uint8_t i = 0; i < size; i++) {
            buffer[i] = 0.0f;
        }
    }
    
    ~MovingAverageFilter() {
        delete[] buffer;
    }
    
    float update(float value) {
        sum -= buffer[index];
        buffer[index] = value;
        sum += value;
        
        index = (index + 1) % size;
        if (index == 0) filled = true;
        
        return sum / (filled ? size : (index == 0 ? size : index));
    }
    
    void reset() {
        for (uint8_t i = 0; i < size; i++) {
            buffer[i] = 0.0f;
        }
        sum = 0.0f;
        index = 0;
        filled = false;
    }
};

// 滤波器实例
KalmanFilter bean_temp_kalman;
KalmanFilter env_temp_kalman;
MovingAverageFilter pot_moving_avg(8);
MovingAverageFilter pressure_moving_avg(4);

// ============================================================================
// 传感器管理任务实现
// ============================================================================

void Task_Sensor_Manager(void *pvParameters) {
    TickType_t xLastWakeTime = xTaskGetTickCount();
    SensorData_t raw_sensor_data;
    
    Serial.println("[SENSOR] 传感器管理任务启动 - 优先级2, 5Hz");
    
    // 初始化传感器硬件
    if (!init_sensors()) {
        Serial.println("[SENSOR] ❌ 传感器初始化失败");
        vTaskDelete(NULL);
        return;
    }
    
    // 传感器状态变量
    uint32_t sensor_read_count = 0;
    uint32_t sensor_error_count = 0;
    float last_valid_bean_temp = 25.0f;
    float last_valid_env_temp = 25.0f;
    
    while(1) {
        bool data_valid = true;
        raw_sensor_data.timestamp = xTaskGetTickCount();
        
        // 1. 读取豆温传感器（热电偶）
        if (sensor_config.thermocouple_enabled) {
            if (xSemaphoreTake(xMutexSPI, pdMS_TO_TICKS(100)) == pdTRUE) {
                raw_sensor_data.bean_temperature = read_thermocouple();
                xSemaphoreGive(xMutexSPI);
                
                // 数据有效性检查
                if (raw_sensor_data.bean_temperature < -50.0f || 
                    raw_sensor_data.bean_temperature > 300.0f) {
                    raw_sensor_data.bean_temperature = last_valid_bean_temp;
                    sensor_error_count++;
                    data_valid = false;
                } else {
                    last_valid_bean_temp = raw_sensor_data.bean_temperature;
                }
            } else {
                Serial.println("[SENSOR] ⚠️ SPI互斥锁获取超时");
                raw_sensor_data.bean_temperature = last_valid_bean_temp;
                data_valid = false;
            }
        }
        
        // 2. 读取环温传感器
        if (sensor_config.env_temp_enabled) {
            raw_sensor_data.env_temperature = read_env_temperature();
            
            // 数据有效性检查
            if (raw_sensor_data.env_temperature < -20.0f || 
                raw_sensor_data.env_temperature > 100.0f) {
                raw_sensor_data.env_temperature = last_valid_env_temp;
                sensor_error_count++;
                data_valid = false;
            } else {
                last_valid_env_temp = raw_sensor_data.env_temperature;
            }
        }
        
        // 3. 读取电位器
        if (sensor_config.pot_enabled) {
            raw_sensor_data.pot_value = read_potentiometer();
        }

        // 4. 读取按键状态
        check_button_freertos();

        // 5. 读取压力传感器（可选）
        if (sensor_config.pressure_enabled) {
            // raw_sensor_data.pressure = read_pressure_sensor();
        }
        
        raw_sensor_data.valid = data_valid;
        sensor_read_count++;
        
        // 5. 发送原始数据到滤波处理队列
        if (xQueueSend(xQueueSensorData, &raw_sensor_data, 0) != pdTRUE) {
            Serial.println("[SENSOR] ⚠️ 传感器数据队列满");
        }
        
        // 6. 传感器自诊断
        if (sensor_read_count % 25 == 0) { // 每5秒检查一次
            float error_rate = (float)sensor_error_count / sensor_read_count * 100.0f;
            if (error_rate > 10.0f) {
                Serial.printf("[SENSOR] ⚠️ 传感器错误率高: %.1f%%\n", error_rate);
            }
        }
        
        // 7. 定期状态报告
        if (sensor_read_count % 250 == 0) { // 每50秒报告一次
            Serial.printf("[SENSOR] 读取:%lu次 错误:%lu次 豆温:%.1f°C 环温:%.1f°C\n",
                         sensor_read_count, sensor_error_count,
                         raw_sensor_data.bean_temperature, raw_sensor_data.env_temperature);
        }
        
        vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(CYCLE_SENSOR_MANAGER));
    }
}

// ============================================================================
// 滤波处理任务实现
// ============================================================================

void Task_Filter_Process(void *pvParameters) {
    TickType_t xLastWakeTime = xTaskGetTickCount();
    SensorData_t raw_data, filtered_data;
    
    Serial.println("[FILTER] 滤波处理任务启动 - 优先级2, 10Hz");
    
    // 初始化滤波器
    bean_temp_kalman.setParams(0.1f, 0.5f);  // 调整噪声参数
    env_temp_kalman.setParams(0.05f, 0.3f);
    
    uint32_t filter_cycles = 0;
    
    while(1) {
        // 1. 获取原始传感器数据
        if (xQueueReceive(xQueueSensorData, &raw_data, pdMS_TO_TICKS(50)) == pdTRUE) {
            
            // 2. 应用滤波算法
            if (raw_data.valid) {
                // 豆温卡尔曼滤波
                filtered_data.bean_temperature = bean_temp_kalman.update(raw_data.bean_temperature);
                
                // 环温卡尔曼滤波
                filtered_data.env_temperature = env_temp_kalman.update(raw_data.env_temperature);
                
                // 电位器移动平均滤波
                filtered_data.pot_value = pot_moving_avg.update(raw_data.pot_value);
                
            } else {
                // 数据无效时保持上次滤波结果
                // filtered_data保持不变
            }
            
            filtered_data.valid = raw_data.valid;
            filtered_data.timestamp = xTaskGetTickCount();
            
            // 3. 数据后处理
            apply_sensor_calibration(&filtered_data);
            
            // 4. 异常值检测和处理
            if (detect_sensor_anomalies(&filtered_data)) {
                Serial.println("[FILTER] ⚠️ 检测到传感器异常值");
                // 可以选择丢弃数据或使用预测值
            }
            
            // 5. 更新共享数据
            if (xSemaphoreTake(xMutexSharedData, pdMS_TO_TICKS(10)) == pdTRUE) {
                g_system_state.current_sensor_data = filtered_data;
                xSemaphoreGive(xMutexSharedData);
            }
            
            // 6. 发送到显示和记录队列
            DisplayData_t display_data;
            display_data.bean_temp = filtered_data.bean_temperature;
            display_data.env_temp = filtered_data.env_temperature;
            display_data.power_percent = RealTime_Get_Current_Power();
            display_data.roast_time = (xTaskGetTickCount() / 1000);
            display_data.status = DISPLAY_STATUS_ROASTING; // 需要根据实际状态设置
            strcpy(display_data.message, "正常运行");
            
            xQueueSend(xQueueDisplayData, &display_data, 0);
            
            // 7. 数据记录
            LogData_t log_data;
            log_data.timestamp = filtered_data.timestamp;
            log_data.bean_temp = filtered_data.bean_temperature;
            log_data.env_temp = filtered_data.env_temperature;
            log_data.power_percent = display_data.power_percent;
            log_data.pid_output = Output; // 从PID控制器获取
            log_data.ror = (uint16_t)calculate_ror(filtered_data.bean_temperature);
            
            xQueueSend(xQueueLogData, &log_data, 0);
        }
        
        filter_cycles++;
        
        // 8. 滤波器性能监控
        if (filter_cycles % 600 == 0) { // 每60秒报告一次
            Serial.printf("[FILTER] 滤波周期: %lu, 队列深度: %d\n", 
                         filter_cycles, uxQueueMessagesWaiting(xQueueSensorData));
        }
        
        vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(CYCLE_FILTER_PROCESS));
    }
}

// ============================================================================
// 传感器硬件接口实现
// ============================================================================

bool init_sensors(void) {
    Serial.println("[SENSOR] 初始化传感器硬件...");
    
    // 1. 初始化SPI（热电偶）
    SPI.begin(THERMOCOUPLE_SCK_PIN, THERMOCOUPLE_SO_PIN, -1, THERMOCOUPLE_CS_PIN);
    SPI.setFrequency(1000000); // 1MHz
    pinMode(THERMOCOUPLE_CS_PIN, OUTPUT);
    digitalWrite(THERMOCOUPLE_CS_PIN, HIGH);
    
    // 2. 初始化环温传感器
    pinMode(ENV_TEMP_PIN, INPUT);
    
    // 3. 初始化ADC
    analogReadResolution(12); // 12位分辨率
    analogSetAttenuation(ADC_11db); // 0-3.3V范围
    
    // 4. 传感器自检
    float test_temp = read_thermocouple();
    if (test_temp < -100.0f || test_temp > 400.0f) {
        Serial.printf("[SENSOR] ⚠️ 热电偶自检异常: %.1f°C\n", test_temp);
        return false;
    }
    
    Serial.println("[SENSOR] ✅ 传感器硬件初始化完成");
    return true;
}

float read_thermocouple(void) {
    // MAX6675热电偶读取实现
    digitalWrite(THERMOCOUPLE_CS_PIN, LOW);
    delayMicroseconds(sensor_config.settling_time_us);
    
    uint16_t raw_data = 0;
    
    // 多次采样求平均
    for (uint8_t i = 0; i < sensor_config.oversample_count; i++) {
        raw_data += SPI.transfer16(0x0000);
        delayMicroseconds(100);
    }
    raw_data /= sensor_config.oversample_count;
    
    digitalWrite(THERMOCOUPLE_CS_PIN, HIGH);
    
    // 检查错误位
    if (raw_data & 0x04) {
        Serial.println("[SENSOR] ⚠️ 热电偶开路");
        return -999.0f;
    }
    
    // 转换为温度
    raw_data >>= 3; // 移除状态位
    float temperature = raw_data * 0.25f; // 0.25°C分辨率
    
    // 应用校准
    temperature = temperature * sensor_config.thermocouple_scale + sensor_config.thermocouple_offset;
    
    return temperature;
}

float read_env_temperature(void) {
    // 简化的环温读取（实际应根据具体传感器实现）
    // 这里假设使用模拟温度传感器
    int raw_adc = analogRead(ENV_TEMP_PIN);
    
    // 转换为电压
    float voltage = (raw_adc / 4095.0f) * 3.3f;
    
    // 转换为温度（假设线性关系）
    float temperature = (voltage - 0.5f) * 100.0f; // 例如TMP36传感器
    
    // 应用校准
    temperature = temperature * sensor_config.env_temp_scale + sensor_config.env_temp_offset;
    
    return temperature;
}

float read_potentiometer(void) {
    int raw_adc = analogRead(POT_PIN);
    return (raw_adc / 4095.0f) * 100.0f; // 转换为0-100%
}



// 应用传感器校准
void apply_sensor_calibration(SensorData_t* data) {
    // 简化的校准实现
    data->bean_temperature += 0.0f; // 校准偏移
    data->env_temperature += 0.0f;
}

// 检测传感器异常
bool detect_sensor_anomalies(const SensorData_t* data) {
    // 检查温度范围
    if (data->bean_temperature < -10.0f || data->bean_temperature > 300.0f) {
        return true;
    }
    if (data->env_temperature < -20.0f || data->env_temperature > 80.0f) {
        return true;
    }
    return false;
}

// 计算升温速率
float calculate_ror(float current_temp) {
    static float last_temp = 0.0f;
    static uint32_t last_time = 0;

    uint32_t current_time = millis();
    if (last_time == 0) {
        last_temp = current_temp;
        last_time = current_time;
        return 0.0f;
    }

    float dt = (current_time - last_time) / 1000.0f; // 转换为秒
    float ror = (current_temp - last_temp) / dt;

    last_temp = current_temp;
    last_time = current_time;

    return ror;
}

// 打印数据处理状态
void DataProcessing_Print_Status(void) {
    Serial.println("========== 数据处理层状态 ==========");

    // 获取当前传感器数据
    SensorData_t sensor_data;
    if (System_Get_Sensor_Data(&sensor_data, pdMS_TO_TICKS(100))) {
        Serial.printf("豆温: %.1f°C\n", sensor_data.bean_temperature);
        Serial.printf("环温: %.1f°C\n", sensor_data.env_temperature);
        Serial.printf("电位器: %.1f%%\n", sensor_data.pot_value);
        Serial.printf("数据有效: %s\n", sensor_data.valid ? "是" : "否");
    } else {
        Serial.println("无法获取传感器数据");
    }

    Serial.println("==================================");
}

// ============================================================================
// 按键处理函数 - FreeRTOS版本
// ============================================================================

void check_button_freertos(void) {
    static bool lastButtonState = HIGH;
    static uint32_t lastDebounceTime = 0;
    static uint32_t buttonPressTime = 0;
    const uint32_t debounceDelay = 50;    // 防抖延迟50ms
    const uint32_t longPressTime = 2000;  // 长按时间2秒

    bool currentButtonState = digitalRead(BUTTON_PIN);
    uint32_t currentTime = xTaskGetTickCount() * portTICK_PERIOD_MS;

    // 防抖处理
    if (currentButtonState != lastButtonState) {
        lastDebounceTime = currentTime;
    }

    if ((currentTime - lastDebounceTime) > debounceDelay) {
        // 检测按键按下
        if (lastButtonState == HIGH && currentButtonState == LOW) {
            buttonPressTime = currentTime;
            Serial.println("[BUTTON] 按键按下 - FreeRTOS");
        }

        // 检测按键释放
        if (lastButtonState == LOW && currentButtonState == HIGH) {
            uint32_t pressDuration = currentTime - buttonPressTime;

            if (pressDuration >= longPressTime) {
                // 长按：发送模式切换命令
                handle_long_press_freertos();
            } else if (pressDuration >= debounceDelay) {
                // 短按：发送快速操作命令
                handle_short_press_freertos();
            }
        }
    }

    lastButtonState = currentButtonState;
}

void handle_short_press_freertos(void) {
    Serial.println("[BUTTON] 短按检测 - FreeRTOS");

    // 发送控制命令到控制算法任务
    ControlCommand_t cmd;
    cmd.command_type = ControlCommand_t::CMD_SET_POWER;
    cmd.params.set_power.power_percent = 30.0f; // 示例：设置30%功率
    cmd.timestamp = xTaskGetTickCount();

    if (xQueueControlCmd) {
        xQueueSend(xQueueControlCmd, &cmd, 0);
        Serial.println("[BUTTON] 短按命令已发送");
    }
}

void handle_long_press_freertos(void) {
    Serial.println("[BUTTON] 长按检测 - 模式切换 - FreeRTOS");

    // 发送紧急停止命令
    ControlCommand_t cmd;
    cmd.command_type = ControlCommand_t::CMD_STOP_ROAST;
    cmd.timestamp = xTaskGetTickCount();

    if (xQueueControlCmd) {
        xQueueSend(xQueueControlCmd, &cmd, 0);
        Serial.println("[BUTTON] 长按停止命令已发送");
    }
}

// ============================================================================
// 数据处理工具函数
// ============================================================================


