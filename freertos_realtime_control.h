#ifndef FREERTOS_REALTIME_CONTROL_H
#define FREERTOS_REALTIME_CONTROL_H

#include "freertos_architecture_design.h"

// ============================================================================
// FreeRTOS实时控制头文件
// ============================================================================

// 实时控制任务
void Task_RealTime_Control(void *pvParameters);

// PWM控制函数
bool RealTime_Set_PWM_Power(float power_percent);
float RealTime_Get_Current_Power(void);
bool RealTime_Emergency_Stop(void);

// 安全保护函数
bool RealTime_Safety_Check(void);
void RealTime_Apply_Safety_Limits(float *power, float *fan_speed);

// 输出更新函数
void RealTime_Update_Outputs(void);
void RealTime_Update_PWM_Channels(void);

#endif // FREERTOS_REALTIME_CONTROL_H
